<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المخزون - نظام المحاسبة لوكالات السفريات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">نظام المحاسبة لوكالات السفريات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> المدير
                        </a>
                        <ul class="dropdown-menu dropdown-menu-start">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-2 sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reservations.html">
                            <i class="fas fa-calendar-check"></i> الحجوزات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="agents.html">
                            <i class="fas fa-user-tie"></i> الوكلاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.html">
                            <i class="fas fa-truck"></i> الموردون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="inventory.html">
                            <i class="fas fa-warehouse"></i> المخزون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sales.html">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="purchases.html">
                            <i class="fas fa-hand-holding-usd"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="accounts.html">
                            <i class="fas fa-file-invoice-dollar"></i> الحسابات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="finance.html">
                            <i class="fas fa-chart-line"></i> المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="templates.html">
                            <i class="fas fa-file-contract"></i> القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.html">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>المخزون</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addInventoryModal">
                        <i class="fas fa-plus"></i> إضافة مخزون جديد
                    </button>
                </div>

                <!-- تبويبات المخزون -->
                <ul class="nav nav-tabs mb-4" id="inventoryTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="visas-tab" data-bs-toggle="tab" data-bs-target="#visas" type="button" role="tab" aria-controls="visas" aria-selected="true">مخزون التأشيرات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="packages-tab" data-bs-toggle="tab" data-bs-target="#packages" type="button" role="tab" aria-controls="packages" aria-selected="false">الباقات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab" aria-controls="documents" aria-selected="false">الوثائق</button>
                    </li>
                </ul>

                <div class="tab-content" id="inventoryTabsContent">
                    <!-- تبويب مخزون التأشيرات -->
                    <div class="tab-pane fade show active" id="visas" role="tabpanel" aria-labelledby="visas-tab">
                        <!-- فلترة البحث -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <form id="searchVisasForm">
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <label for="searchVisaType" class="form-label">نوع التأشيرة</label>
                                            <select class="form-select" id="searchVisaType">
                                                <option value="">الكل</option>
                                                <option value="individual">فردي</option>
                                                <option value="normal">عادي</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchIssuedNumber" class="form-label">رقم الصادر</label>
                                            <input type="text" class="form-control" id="searchIssuedNumber" placeholder="ابحث برقم الصادر">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchRecordNumber" class="form-label">رقم السجل</label>
                                            <input type="text" class="form-control" id="searchRecordNumber" placeholder="ابحث برقم السجل">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchCompanyName" class="form-label">اسم الشركة</label>
                                            <input type="text" class="form-control" id="searchCompanyName" placeholder="ابحث باسم الشركة">
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> بحث
                                            </button>
                                            <button type="reset" class="btn btn-secondary">
                                                <i class="fas fa-redo"></i> إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- جدول مخزون التأشيرات -->
                        <div class="card">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">مخزون التأشيرات</h5>
                                <div>
                                    <button class="btn btn-sm btn-light">
                                        <i class="fas fa-file-excel"></i> تصدير إكسل
                                    </button>
                                    <button class="btn btn-sm btn-light">
                                        <i class="fas fa-file-pdf"></i> تصدير PDF
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم المخزون</th>
                                                <th>نوع التأشيرة</th>
                                                <th>رقم الصادر</th>
                                                <th>رقم السجل</th>
                                                <th>اسم الشركة</th>
                                                <th>العدد الإجمالي</th>
                                                <th>العدد المستخدم</th>
                                                <th>العدد المتبقي</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#3001</td>
                                                <td>فردي</td>
                                                <td>12345</td>
                                                <td>67890</td>
                                                <td>شركة الأمل للخدمات</td>
                                                <td>50</td>
                                                <td>25</td>
                                                <td>25</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#3002</td>
                                                <td>عادي</td>
                                                <td>23456</td>
                                                <td>78901</td>
                                                <td>شركة النور للسفر</td>
                                                <td>100</td>
                                                <td>60</td>
                                                <td>40</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#3003</td>
                                                <td>فردي</td>
                                                <td>34567</td>
                                                <td>89012</td>
                                                <td>شركة اليمن الدولية</td>
                                                <td>30</td>
                                                <td>10</td>
                                                <td>20</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب الباقات -->
                    <div class="tab-pane fade" id="packages" role="tabpanel" aria-labelledby="packages-tab">
                        <div class="card mb-4">
                            <div class="card-body">
                                <form id="searchPackagesForm">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label for="searchPackageName" class="form-label">اسم الباقة</label>
                                            <input type="text" class="form-control" id="searchPackageName" placeholder="ابحث باسم الباقة">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="searchPackageType" class="form-label">نوع الباقة</label>
                                            <select class="form-select" id="searchPackageType">
                                                <option value="">الكل</option>
                                                <option value="haj">حج</option>
                                                <option value="umrah">عمرة</option>
                                                <option value="tourism">سياحة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="searchPackageStatus" class="form-label">الحالة</label>
                                            <select class="form-select" id="searchPackageStatus">
                                                <option value="">الكل</option>
                                                <option value="available">متاح</option>
                                                <option value="unavailable">غير متاح</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> بحث
                                            </button>
                                            <button type="reset" class="btn btn-secondary">
                                                <i class="fas fa-redo"></i> إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">الباقات</h5>
                                <div>
                                    <button class="btn btn-sm btn-light">
                                        <i class="fas fa-file-excel"></i> تصدير إكسل
                                    </button>
                                    <button class="btn btn-sm btn-light">
                                        <i class="fas fa-file-pdf"></i> تصدير PDF
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم الباقة</th>
                                                <th>اسم الباقة</th>
                                                <th>النوع</th>
                                                <th>السعر</th>
                                                <th>المدة</th>
                                                <th>العدد المتاح</th>
                                                <th>العدد المحجوز</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#4001</td>
                                                <td>باقة العمرة الفضية</td>
                                                <td>عمرة</td>
                                                <td>250,000 ريال</td>
                                                <td>10 أيام</td>
                                                <td>50</td>
                                                <td>30</td>
                                                <td><span class="badge bg-success">متاح</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#4002</td>
                                                <td>باقة الحج الذهبية</td>
                                                <td>حج</td>
                                                <td>1,500,000 ريال</td>
                                                <td>20 يوماً</td>
                                                <td>30</td>
                                                <td>25</td>
                                                <td><span class="badge bg-success">متاح</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب الوثائق -->
                    <div class="tab-pane fade" id="documents" role="tabpanel" aria-labelledby="documents-tab">
                        <div class="card mb-4">
                            <div class="card-body">
                                <form id="searchDocumentsForm">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label for="searchDocumentName" class="form-label">اسم الوثيقة</label>
                                            <input type="text" class="form-control" id="searchDocumentName" placeholder="ابحث باسم الوثيقة">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="searchDocumentType" class="form-label">نوع الوثيقة</label>
                                            <select class="form-select" id="searchDocumentType">
                                                <option value="">الكل</option>
                                                <option value="passport">جواز سفر</option>
                                                <option value="id">بطاقة شخصية</option>
                                                <option value="certificate">شهادة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="searchDocumentStatus" class="form-label">الحالة</label>
                                            <select class="form-select" id="searchDocumentStatus">
                                                <option value="">الكل</option>
                                                <option value="available">متاح</option>
                                                <option value="used">مستخدم</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> بحث
                                            </button>
                                            <button type="reset" class="btn btn-secondary">
                                                <i class="fas fa-redo"></i> إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">الوثائق</h5>
                                <div>
                                    <button class="btn btn-sm btn-light">
                                        <i class="fas fa-file-excel"></i> تصدير إكسل
                                    </button>
                                    <button class="btn btn-sm btn-light">
                                        <i class="fas fa-file-pdf"></i> تصدير PDF
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم الوثيقة</th>
                                                <th>اسم الوثيقة</th>
                                                <th>النوع</th>
                                                <th>العدد الإجمالي</th>
                                                <th>العدد المستخدم</th>
                                                <th>العدد المتبقي</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#5001</td>
                                                <td>نماذج جوازات السفر</td>
                                                <td>جواز سفر</td>
                                                <td>100</td>
                                                <td>60</td>
                                                <td>40</td>
                                                <td><span class="badge bg-success">متاح</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#5002</td>
                                                <td>نماذج البطاقات الشخصية</td>
                                                <td>بطاقة شخصية</td>
                                                <td>150</td>
                                                <td>90</td>
                                                <td>60</td>
                                                <td><span class="badge bg-success">متاح</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مخزون جديد -->
    <div class="modal fade" id="addInventoryModal" tabindex="-1" aria-labelledby="addInventoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addInventoryModalLabel">إضافة مخزون جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addInventoryForm">
                        <ul class="nav nav-tabs mb-3" id="inventoryTypeTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="visa-tab" data-bs-toggle="tab" data-bs-target="#visa" type="button" role="tab" aria-controls="visa" aria-selected="true">تأشيرة</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="package-tab" data-bs-toggle="tab" data-bs-target="#package" type="button" role="tab" aria-controls="package" aria-selected="false">باقة</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="document-tab" data-bs-toggle="tab" data-bs-target="#document" type="button" role="tab" aria-controls="document" aria-selected="false">وثيقة</button>
                            </li>
                        </ul>
                        <div class="tab-content" id="inventoryTypeTabsContent">
                            <!-- نموذج إضافة تأشيرة -->
                            <div class="tab-pane fade show active" id="visa" role="tabpanel" aria-labelledby="visa-tab">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="visaType" class="form-label">نوع التأشيرة</label>
                                        <select class="form-select" id="visaType" required>
                                            <option value="">اختر نوع التأشيرة</option>
                                            <option value="individual">فردي</option>
                                            <option value="normal">عادي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="issuedNumber" class="form-label">رقم الصادر</label>
                                        <input type="text" class="form-control" id="issuedNumber" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="recordNumber" class="form-label">رقم السجل</label>
                                        <input type="text" class="form-control" id="recordNumber" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="companyName" class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-control" id="companyName" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="totalCount" class="form-label">العدد الإجمالي</label>
                                        <input type="number" class="form-control" id="totalCount" min="1" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="usedCount" class="form-label">العدد المستخدم</label>
                                        <input type="number" class="form-control" id="usedCount" min="0" value="0">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="remainingCount" class="form-label">العدد المتبقي</label>
                                        <input type="number" class="form-control" id="remainingCount" min="0" readonly>
                                    </div>
                                </div>
                            </div>
                            <!-- نموذج إضافة باقة -->
                            <div class="tab-pane fade" id="package" role="tabpanel" aria-labelledby="package-tab">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="packageName" class="form-label">اسم الباقة</label>
                                        <input type="text" class="form-control" id="packageName" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="packageType" class="form-label">نوع الباقة</label>
                                        <select class="form-select" id="packageType" required>
                                            <option value="">اختر نوع الباقة</option>
                                            <option value="haj">حج</option>
                                            <option value="umrah">عمرة</option>
                                            <option value="tourism">سياحة</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="packagePrice" class="form-label">السعر</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="packagePrice" min="0" required>
                                            <select class="form-select" id="packageCurrency" style="max-width: 120px;">
                                                <option value="yer">ريال يمني</option>
                                                <option value="sar">ريال سعودي</option>
                                                <option value="usd">دولار</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="packageDuration" class="form-label">المدة</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="packageDuration" min="1" required>
                                            <select class="form-select" id="packageDurationUnit" style="max-width: 120px;">
                                                <option value="days">أيام</option>
                                                <option value="weeks">أسابيع</option>
                                                <option value="months">أشهر</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="availableCount" class="form-label">العدد المتاح</label>
                                        <input type="number" class="form-control" id="availableCount" min="1" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="bookedCount" class="form-label">العدد المحجوز</label>
                                        <input type="number" class="form-control" id="bookedCount" min="0" value="0">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="packageStatus" class="form-label">الحالة</label>
                                        <select class="form-select" id="packageStatus">
                                            <option value="available">متاح</option>
                                            <option value="unavailable">غير متاح</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label for="packageDescription" class="form-label">الوصف</label>
                                        <textarea class="form-control" id="packageDescription" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                            <!-- نموذج إضافة وثيقة -->
                            <div class="tab-pane fade" id="document" role="tabpanel" aria-labelledby="document-tab">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="documentName" class="form-label">اسم الوثيقة</label>
                                        <input type="text" class="form-control" id="documentName" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="documentType" class="form-label">نوع الوثيقة</label>
                                        <select class="form-select" id="documentType" required>
                                            <option value="">اختر نوع الوثيقة</option>
                                            <option value="passport">جواز سفر</option>
                                            <option value="id">بطاقة شخصية</option>
                                            <option value="certificate">شهادة</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="documentTotalCount" class="form-label">العدد الإجمالي</label>
                                        <input type="number" class="form-control" id="documentTotalCount" min="1" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="documentUsedCount" class="form-label">العدد المستخدم</label>
                                        <input type="number" class="form-control" id="documentUsedCount" min="0" value="0">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="documentRemainingCount" class="form-label">العدد المتبقي</label>
                                        <input type="number" class="form-control" id="documentRemainingCount" min="0" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="documentStatus" class="form-label">الحالة</label>
                                        <select class="form-select" id="documentStatus">
                                            <option value="available">متاح</option>
                                            <option value="used">مستخدم</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="documentExpiryDate" class="form-label">تاريخ الانتهاء</label>
                                        <input type="date" class="form-control" id="documentExpiryDate">
                                    </div>
                                    <div class="col-12">
                                        <label for="documentDescription" class="form-label">الوصف</label>
                                        <textarea class="form-control" id="documentDescription" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveInventory()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/inventory.js"></script>
</body>
</html>