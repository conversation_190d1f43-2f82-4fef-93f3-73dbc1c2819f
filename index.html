<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة لوكالات السفريات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* أنماط إضافية لصفحة تسجيل الدخول */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            z-index: -2;
        }

        .login-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSg0NSkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjAzKSIvPjwvcGF0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+');
            opacity: 0.3;
            z-index: -1;
        }

        .login-card {
            width: 100%;
            max-width: 450px;
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 25px;
            border: 1px solid var(--glass-border);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
            z-index: 1;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }

        .login-header {
            text-align: center;
            padding: 30px 20px 20px;
            background: var(--primary-gradient);
            position: relative;
            overflow: hidden;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0% { transform: scale(0.8); opacity: 0.5; }
            50% { transform: scale(1.2); opacity: 0.8; }
            100% { transform: scale(0.8); opacity: 0.5; }
        }

        .login-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 1;
        }

        .login-logo i {
            font-size: 36px;
            color: white;
        }

        .login-title {
            color: white;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        .login-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            margin-bottom: 0;
            position: relative;
            z-index: 1;
        }

        .login-body {
            padding: 30px;
        }

        .nav-tabs {
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 25px;
        }

        .nav-tabs .nav-link {
            color: rgba(255, 255, 255, 0.7);
            border: none;
            border-bottom: 2px solid transparent;
            border-radius: 0;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link:hover {
            color: white;
            border-bottom: 2px solid rgba(255, 255, 255, 0.5);
        }

        .nav-tabs .nav-link.active {
            color: white;
            background: transparent;
            border-bottom: 2px solid var(--neon-blue);
            box-shadow: 0 1px 0 var(--neon-blue);
        }

        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 15px;
            padding: 12px 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--neon-blue);
            box-shadow: 0 0 0 0.25rem rgba(0, 212, 255, 0.25);
            color: white;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-label {
            color: white;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .btn-login {
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            width: 100%;
            margin-top: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            transition: width 0.3s ease;
            z-index: 0;
            border-radius: 50px;
        }

        .btn-login:hover::before {
            width: 100%;
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.6);
        }

        .login-footer {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* تأثيرات النيون للحقول الإدخال عند التركيز */
        .form-group {
            position: relative;
            margin-bottom: 25px;
        }

        .form-group .form-control {
            padding-left: 45px;
        }

        .form-group .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.6);
            transition: all 0.3s ease;
        }

        .form-group .form-control:focus + .input-icon {
            color: var(--neon-blue);
            text-shadow: 0 0 5px var(--neon-blue);
        }

        /* تأثيرات الجسيمات المتحركة */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            animation: float-up 10s infinite linear;
        }

        @keyframes float-up {
            0% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) scale(1);
                opacity: 0;
            }
        }

        /* تأثيرات الوميض */
        @keyframes blink {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 1; }
        }

        .blink {
            animation: blink 2s infinite;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- تأثير الجسيمات المتحركة -->
        <div class="particles" id="particles"></div>

        <div class="login-card">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-plane-departure"></i>
                </div>
                <h2 class="login-title">نظام المحاسبة لوكالات السفريات</h2>
                <p class="login-subtitle">في اليمن</p>
            </div>

            <div class="login-body">
                <ul class="nav nav-tabs" id="authTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="login-tab" data-bs-toggle="tab" data-bs-target="#login" type="button" role="tab" aria-controls="login" aria-selected="true">تسجيل الدخول</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="register-tab" data-bs-toggle="tab" data-bs-target="#register" type="button" role="tab" aria-controls="register" aria-selected="false">حساب جديد</button>
                    </li>
                </ul>

                <div class="tab-content" id="authTabsContent">
                    <div class="tab-pane fade show active" id="login" role="tabpanel" aria-labelledby="login-tab">
                        <form id="loginForm">
                            <div class="form-group">
                                <input type="email" class="form-control" id="loginEmail" placeholder="البريد الإلكتروني" required>
                                <i class="fas fa-envelope input-icon"></i>
                            </div>
                            <div class="form-group">
                                <input type="password" class="form-control" id="loginPassword" placeholder="كلمة المرور" required>
                                <i class="fas fa-lock input-icon"></i>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe">
                                    <label class="form-check-label text-white" for="rememberMe">
                                        تذكرني
                                    </label>
                                </div>
                                <a href="#" class="text-white">نسيت كلمة المرور؟</a>
                            </div>
                            <button type="submit" class="btn btn-login">دخول</button>
                        </form>
                    </div>

                    <div class="tab-pane fade" id="register" role="tabpanel" aria-labelledby="register-tab">
                        <form id="registerForm">
                            <div class="form-group">
                                <input type="text" class="form-control" id="registerName" placeholder="الاسم الكامل" required>
                                <i class="fas fa-user input-icon"></i>
                            </div>
                            <div class="form-group">
                                <input type="email" class="form-control" id="registerEmail" placeholder="البريد الإلكتروني" required>
                                <i class="fas fa-envelope input-icon"></i>
                            </div>
                            <div class="form-group">
                                <input type="password" class="form-control" id="registerPassword" placeholder="كلمة المرور" required>
                                <i class="fas fa-lock input-icon"></i>
                            </div>
                            <div class="form-group">
                                <input type="password" class="form-control" id="registerConfirmPassword" placeholder="تأكيد كلمة المرور" required>
                                <i class="fas fa-lock input-icon"></i>
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-control" id="registerAgency" placeholder="اسم الوكالة" required>
                                <i class="fas fa-building input-icon"></i>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                <label class="form-check-label text-white" for="agreeTerms">
                                    أوافق على <a href="#" class="text-white">الشروط والأحكام</a>
                                </label>
                            </div>
                            <button type="submit" class="btn btn-login">إنشاء حساب</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="login-footer">
                <p>جميع الحقوق محفوظة © 2023 - نظام المحاسبة لوكالات السفريات</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        // إنشاء تأثير الجسيمات المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            const particlesContainer = document.getElementById('particles');
            const particlesCount = 30;

            for (let i = 0; i < particlesCount; i++) {
                const particle = document.createElement('div');
                particle.classList.add('particle');

                // حجم عشوائي للجسيم
                const size = Math.random() * 5 + 2;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;

                // موضع عشوائي للجسيم
                const posX = Math.random() * 100;
                particle.style.left = `${posX}%`;

                // مدة عشوائية للتحريك
                const duration = Math.random() * 20 + 10;
                particle.style.animationDuration = `${duration}s`;

                // تأخير عشوائي للتحريك
                const delay = Math.random() * 5;
                particle.style.animationDelay = `${delay}s`;

                particlesContainer.appendChild(particle);
            }

            // معالجة نموذج تسجيل الدخول
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // محاكاة عملية تسجيل الدخول
                    const email = document.getElementById('loginEmail').value;
                    const password = document.getElementById('loginPassword').value;

                    // إظهار رسالة تحميل
                    const submitBtn = loginForm.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري تسجيل الدخول...';
                    submitBtn.disabled = true;

                    // محاكاة طلب الخادم
                    setTimeout(() => {
                        // استعادة الزر
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;

                        // توجيه المستخدم إلى لوحة التحكم
                        window.location.href = 'dashboard.html';
                    }, 2000);
                });
            }

            // معالجة نموذج إنشاء حساب جديد
            const registerForm = document.getElementById('registerForm');
            if (registerForm) {
                registerForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const password = document.getElementById('registerPassword').value;
                    const confirmPassword = document.getElementById('registerConfirmPassword').value;

                    // التحقق من تطابق كلمتي المرور
                    if (password !== confirmPassword) {
                        alert('كلمتا المرور غير متطابقتين!');
                        return;
                    }

                    // محاكاة عملية إنشاء الحساب
                    const submitBtn = registerForm.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري إنشاء الحساب...';
                    submitBtn.disabled = true;

                    // محاكاة طلب الخادم
                    setTimeout(() => {
                        // استعادة الزر
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;

                        // إظهار رسالة نجاح
                        alert('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.');

                        // التبديل إلى تبويب تسجيل الدخول
                        document.getElementById('login-tab').click();

                        // إعادة تعيين النموذج
                        registerForm.reset();
                    }, 2000);
                });
            }
        });
    </script>
</body>
</html>