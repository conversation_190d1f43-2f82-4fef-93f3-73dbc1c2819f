// JavaScript للنظام المحاسبي لوكالات السفريات

document.addEventListener('DOMContentLoaded', function() {
    // معالجة نموذج تسجيل الدخول
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            // هنا سيتم إضافة منطق التحقق من تسجيل الدخول
            console.log('محاولة تسجيل الدخول:', { email, password });

            // بعد التحقق بنجاح، سيتم توجيه المستخدم إلى الصفحة الرئيسية
            window.location.href = 'dashboard.html';
        });
    }

    // معالجة نموذج إنشاء حساب جديد
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('registerConfirmPassword').value;
            const agency = document.getElementById('registerAgency').value;

            // التحقق من تطابق كلمتي المرور
            if (password !== confirmPassword) {
                alert('كلمتا المرور غير متطابقتين!');
                return;
            }

            // هنا سيتم إضافة منطق إنشاء حساب جديد
            console.log('محاولة إنشاء حساب جديد:', { name, email, password, agency });

            // بعد إنشاء الحساب بنجاح
            alert('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.');

            // التبديل إلى تبويب تسجيل الدخول
            document.getElementById('login-tab').click();

            // إعادة تعيين النموذج
            registerForm.reset();
        });
    }
});

// دالة لتأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من أنك تريد الحذف؟') {
    return confirm(message);
}

// دالة لعرض رسائل النجاح
function showSuccessMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.setAttribute('role', 'alert');
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // إخفاء الرسالة تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            alertDiv.remove();
        }, 150);
    }, 5000);
}

// دالة لعرض رسائل الخطأ
function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.setAttribute('role', 'alert');
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // إخفاء الرسالة تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            alertDiv.remove();
        }, 150);
    }, 5000);
}