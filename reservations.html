<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحجوزات - نظام المحاسبة لوكالات السفريات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">نظام المحاسبة لوكالات السفريات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> المدير
                        </a>
                        <ul class="dropdown-menu dropdown-menu-start">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-2 sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="reservations.html">
                            <i class="fas fa-calendar-check"></i> الحجوزات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="agents.html">
                            <i class="fas fa-user-tie"></i> الوكلاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.html">
                            <i class="fas fa-truck"></i> الموردون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.html">
                            <i class="fas fa-warehouse"></i> المخزون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sales.html">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="purchases.html">
                            <i class="fas fa-hand-holding-usd"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="accounts.html">
                            <i class="fas fa-file-invoice-dollar"></i> الحسابات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="finance.html">
                            <i class="fas fa-chart-line"></i> المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="templates.html">
                            <i class="fas fa-file-contract"></i> القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.html">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-10 main-content">
                <h2 class="mb-4">الحجوزات</h2>

                <!-- بطاقات أنواع الحجوزات -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-primary text-white">
                            <div class="card-body">
                                <i class="fas fa-mosque"></i>
                                <h5>حجوزات العمرة</h5>
                                <h3>45</h3>
                                <button class="btn btn-light btn-sm mt-2" onclick="showReservations('umrah')">
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-success text-white">
                            <div class="card-body">
                                <i class="fas fa-kaaba"></i>
                                <h5>حجوزات الحج</h5>
                                <h3>23</h3>
                                <button class="btn btn-light btn-sm mt-2" onclick="showReservations('hajj')">
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-info text-white">
                            <div class="card-body">
                                <i class="fas fa-passport"></i>
                                <h5>حجوزات الجوازات</h5>
                                <h3>67</h3>
                                <button class="btn btn-light btn-sm mt-2" onclick="showReservations('passport')">
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-warning text-white">
                            <div class="card-body">
                                <i class="fas fa-bus"></i>
                                <h5>حجوزات الباصات</h5>
                                <h3>34</h3>
                                <button class="btn btn-light btn-sm mt-2" onclick="showReservations('bus')">
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-danger text-white">
                            <div class="card-body">
                                <i class="fas fa-car"></i>
                                <h5>حجوزات السيارات</h5>
                                <h3>28</h3>
                                <button class="btn btn-light btn-sm mt-2" onclick="showReservations('car')">
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-secondary text-white">
                            <div class="card-body">
                                <i class="fas fa-plane"></i>
                                <h5>حجوزات الطيران</h5>
                                <h3>56</h3>
                                <button class="btn btn-light btn-sm mt-2" onclick="showReservations('flight')">
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-dark text-white">
                            <div class="card-body">
                                <i class="fas fa-file-contract"></i>
                                <h5>تعميد الوثائق</h5>
                                <h3>19</h3>
                                <button class="btn btn-light btn-sm mt-2" onclick="showReservations('document')">
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويبات الحجوزات -->
                <ul class="nav nav-tabs mb-4" id="reservationTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="umrah-tab" data-bs-toggle="tab" data-bs-target="#umrah" type="button" role="tab" aria-controls="umrah" aria-selected="true">حجز عمرة</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="hajj-tab" data-bs-toggle="tab" data-bs-target="#hajj" type="button" role="tab" aria-controls="hajj" aria-selected="false">حجز حج</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="passport-tab" data-bs-toggle="tab" data-bs-target="#passport" type="button" role="tab" aria-controls="passport" aria-selected="false">حجز جوازات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="bus-tab" data-bs-toggle="tab" data-bs-target="#bus" type="button" role="tab" aria-controls="bus" aria-selected="false">حجز باصات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="car-tab" data-bs-toggle="tab" data-bs-target="#car" type="button" role="tab" aria-controls="car" aria-selected="false">حجز سيارات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="flight-tab" data-bs-toggle="tab" data-bs-target="#flight" type="button" role="tab" aria-controls="flight" aria-selected="false">حجز طيران</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="document-tab" data-bs-toggle="tab" data-bs-target="#document" type="button" role="tab" aria-controls="document" aria-selected="false">تعميد وثائق</button>
                    </li>
                </ul>

                <div class="d-flex justify-content-end mb-3">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addReservationModal">
                        <i class="fas fa-plus"></i> إضافة حجز جديد
                    </button>
                </div>

                <div class="tab-content" id="reservationTabsContent">
                    <!-- تبويب حجز عمرة -->
                    <div class="tab-pane fade show active" id="umrah" role="tabpanel" aria-labelledby="umrah-tab">
                        <!-- فلترة البحث -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <form id="searchUmrahForm">
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <label for="searchClientName" class="form-label">اسم العميل</label>
                                            <input type="text" class="form-control" id="searchClientName" placeholder="ابحث باسم العميل">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchReservationDate" class="form-label">تاريخ الحجز</label>
                                            <input type="date" class="form-control" id="searchReservationDate">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchUmrahPackage" class="form-label">الباقة</label>
                                            <select class="form-select" id="searchUmrahPackage">
                                                <option value="">الكل</option>
                                                <option value="economy">اقتصادية</option>
                                                <option value="standard">عادية</option>
                                                <option value="premium">مميزة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchStatus" class="form-label">الحالة</label>
                                            <select class="form-select" id="searchStatus">
                                                <option value="">الكل</option>
                                                <option value="confirmed">مؤكد</option>
                                                <option value="pending">قيد الانتظار</option>
                                                <option value="cancelled">ملغي</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> بحث
                                            </button>
                                            <button type="reset" class="btn btn-secondary">
                                                <i class="fas fa-redo"></i> إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- جدول حجوزات العمرة -->
                        <div class="card">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">حجوزات العمرة</h5>
                                <div>
                                    <button class="btn btn-sm btn-light">
                                        <i class="fas fa-file-excel"></i> تصدير إكسل
                                    </button>
                                    <button class="btn btn-sm btn-light">
                                        <i class="fas fa-file-pdf"></i> تصدير PDF
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم الحجز</th>
                                                <th>اسم العميل</th>
                                                <th>تاريخ الحجز</th>
                                                <th>تاريخ السفر</th>
                                                <th>الباقة</th>
                                                <th>المدة</th>
                                                <th>السعر</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#4001</td>
                                                <td>أحمد محمد</td>
                                                <td>2023-05-10</td>
                                                <td>2023-06-15</td>
                                                <td>عادية</td>
                                                <td>10 أيام</td>
                                                <td>150,000 ريال</td>
                                                <td><span class="badge bg-success">مؤكد</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#4002</td>
                                                <td>علي عبدالله</td>
                                                <td>2023-05-12</td>
                                                <td>2023-07-01</td>
                                                <td>مميزة</td>
                                                <td>15 يوماً</td>
                                                <td>250,000 ريال</td>
                                                <td><span class="badge bg-warning">قيد الانتظار</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#4003</td>
                                                <td>خالد سالم</td>
                                                <td>2023-05-08</td>
                                                <td>2023-05-20</td>
                                                <td>اقتصادية</td>
                                                <td>7 أيام</td>
                                                <td>100,000 ريال</td>
                                                <td><span class="badge bg-danger">ملغي</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب حجز حج -->
                    <div class="tab-pane fade" id="hajj" role="tabpanel" aria-labelledby="hajj-tab">
                        <!-- سيتم ملء محتوى مشابه لتبويب العمرة -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> محتوى حجوزات الحج سيتم إضافته هنا
                        </div>
                    </div>

                    <!-- تبويب حجز جوازات -->
                    <div class="tab-pane fade" id="passport" role="tabpanel" aria-labelledby="passport-tab">
                        <!-- سيتم ملء محتوى مشابه لتبويب العمرة -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> محتوى حجوزات الجوازات سيتم إضافته هنا
                        </div>
                    </div>

                    <!-- تبويب حجز باصات -->
                    <div class="tab-pane fade" id="bus" role="tabpanel" aria-labelledby="bus-tab">
                        <!-- سيتم ملء محتوى مشابه لتبويب العمرة -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> محتوى حجوزات الباصات سيتم إضافته هنا
                        </div>
                    </div>

                    <!-- تبويب حجز سيارات -->
                    <div class="tab-pane fade" id="car" role="tabpanel" aria-labelledby="car-tab">
                        <!-- سيتم ملء محتوى مشابه لتبويب العمرة -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> محتوى حجوزات السيارات سيتم إضافته هنا
                        </div>
                    </div>

                    <!-- تبويب حجز طيران -->
                    <div class="tab-pane fade" id="flight" role="tabpanel" aria-labelledby="flight-tab">
                        <!-- سيتم ملء محتوى مشابه لتبويب العمرة -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> محتوى حجوزات الطيران سيتم إضافته هنا
                        </div>
                    </div>

                    <!-- تبويب تعميد وثائق -->
                    <div class="tab-pane fade" id="document" role="tabpanel" aria-labelledby="document-tab">
                        <!-- سيتم ملء محتوى مشابه لتبويب العمرة -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> محتوى تعميد الوثائق سيتم إضافته هنا
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة حجز جديد -->
    <div class="modal fade" id="addReservationModal" tabindex="-1" aria-labelledby="addReservationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addReservationModalLabel">إضافة حجز جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addReservationForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="reservationType" class="form-label">نوع الحجز</label>
                                <select class="form-select" id="reservationType" required>
                                    <option value="">اختر نوع الحجز</option>
                                    <option value="umrah">عمرة</option>
                                    <option value="hajj">حج</option>
                                    <option value="passport">جوازات</option>
                                    <option value="bus">باصات</option>
                                    <option value="car">سيارات</option>
                                    <option value="flight">طيران</option>
                                    <option value="document">تعميد وثائق</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="clientName" class="form-label">اسم العميل</label>
                                <input type="text" class="form-control" id="clientName" required>
                            </div>
                            <div class="col-md-6">
                                <label for="clientPhone" class="form-label">رقم الجوال</label>
                                <input type="tel" class="form-control" id="clientPhone" required>
                            </div>
                            <div class="col-md-6">
                                <label for="reservationDate" class="form-label">تاريخ الحجز</label>
                                <input type="date" class="form-control" id="reservationDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="startDate" class="form-label">تاريخ البدء</label>
                                <input type="date" class="form-control" id="startDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="endDate" class="form-label">تاريخ الانتهاء</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-6">
                                <label for="packageType" class="form-label">نوع الباقة</label>
                                <select class="form-select" id="packageType">
                                    <option value="">اختر الباقة</option>
                                    <option value="economy">اقتصادية</option>
                                    <option value="standard">عادية</option>
                                    <option value="premium">مميزة</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="price" class="form-label">السعر</label>
                                <input type="number" class="form-control" id="price" required>
                            </div>
                            <div class="col-md-6">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" required>
                                    <option value="pending">قيد الانتظار</option>
                                    <option value="confirmed">مؤكد</option>
                                    <option value="cancelled">ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="agent" class="form-label">الوكيل</label>
                                <select class="form-select" id="agent">
                                    <option value="">اختر الوكيل</option>
                                    <option value="1">وكيل 1</option>
                                    <option value="2">وكيل 2</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveReservation()">حفظ الحجز</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        // دالة لعرض تبويب معين
        function showReservations(type) {
            const tab = document.getElementById(type + '-tab');
            if (tab) {
                tab.click();
            }
        }

        // دالة لحفظ الحجز
        function saveReservation() {
            // هنا سيتم إضافة منطق حفظ الحجز
            const form = document.getElementById('addReservationForm');
            if (form.checkValidity()) {
                // جمع بيانات النموذج
                const formData = new FormData(form);

                // هنا سيتم إرسال البيانات إلى الخادم

                // إغلاق النافذة
                const modal = bootstrap.Modal.getInstance(document.getElementById('addReservationModal'));
                modal.hide();

                // إظهار رسالة نجاح
                showSuccessMessage('تم حفظ الحجز بنجاح');

                // إعادة تعيين النموذج
                form.reset();
            } else {
                form.reportValidity();
            }
        }
    </script>
</body>
</html>