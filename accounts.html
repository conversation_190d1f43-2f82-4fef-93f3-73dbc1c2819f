<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحسابات - نظام المحاسبة لوكالات السفريات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">نظام المحاسبة لوكالات السفريات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> المدير
                        </a>
                        <ul class="dropdown-menu dropdown-menu-start">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-2 sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reservations.html">
                            <i class="fas fa-calendar-check"></i> الحجوزات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="agents.html">
                            <i class="fas fa-user-tie"></i> الوكلاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.html">
                            <i class="fas fa-truck"></i> الموردون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.html">
                            <i class="fas fa-warehouse"></i> المخزون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sales.html">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="purchases.html">
                            <i class="fas fa-hand-holding-usd"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="accounts.html">
                            <i class="fas fa-file-invoice-dollar"></i> الحسابات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="finance.html">
                            <i class="fas fa-chart-line"></i> المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="templates.html">
                            <i class="fas fa-file-contract"></i> القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.html">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-10 main-content">
                <h2 class="mb-4">الحسابات</h2>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-primary text-white">
                            <div class="card-body">
                                <i class="fas fa-file-invoice"></i>
                                <h5>الفواتير الصادرة</h5>
                                <h3>245</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-success text-white">
                            <div class="card-body">
                                <i class="fas fa-file-invoice-dollar"></i>
                                <h5>الفواتير المستحقة</h5>
                                <h3>187</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-info text-white">
                            <div class="card-body">
                                <i class="fas fa-money-bill-wave"></i>
                                <h5>المدفوعات المحصلة</h5>
                                <h3>4,250,000 ريال</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-warning text-white">
                            <div class="card-body">
                                <i class="fas fa-exclamation-triangle"></i>
                                <h5>المدفوعات المتأخرة</h5>
                                <h3>1,250,000 ريال</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويبات الحسابات -->
                <ul class="nav nav-tabs mb-4" id="accountsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab" aria-controls="invoices" aria-selected="true">الفواتير</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments" type="button" role="tab" aria-controls="payments" aria-selected="false">المدفوعات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="expenses-tab" data-bs-toggle="tab" data-bs-target="#expenses" type="button" role="tab" aria-controls="expenses" aria-selected="false">المصروفات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="taxes-tab" data-bs-toggle="tab" data-bs-target="#taxes" type="button" role="tab" aria-controls="taxes" aria-selected="false">الضرائب</button>
                    </li>
                </ul>

                <div class="d-flex justify-content-end mb-3">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addInvoiceModal">
                        <i class="fas fa-plus"></i> إضافة فاتورة جديدة
                    </button>
                </div>

                <div class="tab-content" id="accountsTabsContent">
                    <!-- تبويب الفواتير -->
                    <div class="tab-pane fade show active" id="invoices" role="tabpanel" aria-labelledby="invoices-tab">
                        <!-- فلترة البحث -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <form id="searchInvoicesForm">
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <label for="searchInvoiceNumber" class="form-label">رقم الفاتورة</label>
                                            <input type="text" class="form-control" id="searchInvoiceNumber" placeholder="ابحث برقم الفاتورة">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchClientName" class="form-label">اسم العميل</label>
                                            <input type="text" class="form-control" id="searchClientName" placeholder="ابحث باسم العميل">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchDateFrom" class="form-label">من تاريخ</label>
                                            <input type="date" class="form-control" id="searchDateFrom">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchDateTo" class="form-label">إلى تاريخ</label>
                                            <input type="date" class="form-control" id="searchDateTo">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchStatus" class="form-label">الحالة</label>
                                            <select class="form-select" id="searchStatus">
                                                <option value="">الكل</option>
                                                <option value="paid">مدفوعة</option>
                                                <option value="partial">مدفوعة جزئياً</option>
                                                <option value="unpaid">غير مدفوعة</option>
                                                <option value="overdue">متأخرة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="searchAmountFrom" class="form-label">المبلغ من</label>
                                            <input type="number" class="form-control" id="searchAmountFrom" placeholder="المبلغ الأدنى">
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> بحث
                                            </button>
                                            <button type="reset" class="btn btn-secondary">
                                                <i class="fas fa-redo"></i> إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- جدول الفواتير -->
                        <div class="card">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">قائمة الفواتير</h5>
                                <div>
                                    <button class="btn btn-sm btn-light">
                                        <i class="fas fa-file-excel"></i> تصدير إكسل
                                    </button>
                                    <button class="btn btn-sm btn-light">
                                        <i class="fas fa-file-pdf"></i> تصدير PDF
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>التاريخ</th>
                                                <th>اسم العميل</th>
                                                <th>المبلغ الإجمالي</th>
                                                <th>المدفوع</th>
                                                <th>المتبقي</th>
                                                <th>تاريخ الاستحقاق</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#7001</td>
                                                <td>2023-05-10</td>
                                                <td>أحمد محمد</td>
                                                <td>50,000 ريال</td>
                                                <td>30,000 ريال</td>
                                                <td>20,000 ريال</td>
                                                <td>2023-06-10</td>
                                                <td><span class="badge bg-warning">مدفوعة جزئياً</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-success" title="دفع"><i class="fas fa-money-bill-wave"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#7002</td>
                                                <td>2023-05-12</td>
                                                <td>علي عبدالله</td>
                                                <td>75,000 ريال</td>
                                                <td>75,000 ريال</td>
                                                <td>0 ريال</td>
                                                <td>2023-06-12</td>
                                                <td><span class="badge bg-success">مدفوعة</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-success" title="دفع"><i class="fas fa-money-bill-wave"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#7003</td>
                                                <td>2023-04-20</td>
                                                <td>خالد سالم</td>
                                                <td>45,000 ريال</td>
                                                <td>0 ريال</td>
                                                <td>45,000 ريال</td>
                                                <td>2023-05-20</td>
                                                <td><span class="badge bg-danger">متأخرة</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-success" title="دفع"><i class="fas fa-money-bill-wave"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب المدفوعات -->
                    <div class="tab-pane fade" id="payments" role="tabpanel" aria-labelledby="payments-tab">
                        <!-- سيتم ملء محتوى مشابه لتبويب الفواتير -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> محتوى المدفوعات سيتم إضافته هنا
                        </div>
                    </div>

                    <!-- تبويب المصروفات -->
                    <div class="tab-pane fade" id="expenses" role="tabpanel" aria-labelledby="expenses-tab">
                        <!-- سيتم ملء محتوى مشابه لتبويب الفواتير -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> محتوى المصروفات سيتم إضافته هنا
                        </div>
                    </div>

                    <!-- تبويب الضرائب -->
                    <div class="tab-pane fade" id="taxes" role="tabpanel" aria-labelledby="taxes-tab">
                        <!-- سيتم ملء محتوى مشابه لتبويب الفواتير -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> محتوى الضرائب سيتم إضافته هنا
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة لإضافة فاتورة جديدة -->
    <div class="modal fade" id="addInvoiceModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة فاتورة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addInvoiceForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="invoiceClient" class="form-label">العميل</label>
                                <select class="form-select" id="invoiceClient" required>
                                    <option value="">اختر العميل</option>
                                    <option value="1">أحمد محمد</option>
                                    <option value="2">علي عبدالله</option>
                                    <option value="3">خالد سالم</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="invoiceDate" class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="invoiceDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="invoiceDueDate" class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" id="invoiceDueDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="invoiceStatus" class="form-label">الحالة</label>
                                <select class="form-select" id="invoiceStatus" required>
                                    <option value="unpaid">غير مدفوعة</option>
                                    <option value="partial">مدفوعة جزئياً</option>
                                    <option value="paid">مدفوعة</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">بنود الفاتورة</label>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="invoiceItems">
                                        <thead>
                                            <tr>
                                                <th>الوصف</th>
                                                <th>الكمية</th>
                                                <th>السعر</th>
                                                <th>المبلغ</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><input type="text" class="form-control item-description" placeholder="الوصف"></td>
                                                <td><input type="number" class="form-control item-quantity" placeholder="الكمية" min="1"></td>
                                                <td><input type="number" class="form-control item-price" placeholder="السعر" min="0"></td>
                                                <td><input type="number" class="form-control item-amount" placeholder="المبلغ" min="0" readonly></td>
                                                <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
                                            </tr>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="3" class="text-end fw-bold">الإجمالي:</td>
                                                <td><input type="number" class="form-control" id="invoiceTotal" placeholder="0" readonly></td>
                                                <td></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                <button type="button" class="btn btn-success" id="addItemRow">
                                    <i class="fas fa-plus"></i> إضافة بند
                                </button>
                            </div>
                            <div class="col-md-6">
                                <label for="invoiceNotes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="invoiceNotes" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveInvoice">حفظ الفاتورة</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/accounts.js"></script>
</body>
</html>