/* أنماط الأزرار المحسنة */

/* الأزرار الأساسية */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  padding: var(--space-sm) var(--space-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  z-index: 1;
  border: none;
  cursor: pointer;
  font-family: var(--font-family-primary);
  text-decoration: none;
  box-shadow: var(--shadow-md);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: width var(--transition-normal);
  z-index: -1;
  border-radius: var(--radius-full);
}

.btn:hover::before {
  width: 100%;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.btn:active {
  transform: translateY(-1px);
}

.btn i, .btn svg {
  margin-left: var(--space-sm);
}

/* أحجام الأزرار */
.btn-sm {
  font-size: var(--font-size-sm);
  padding: var(--space-xs) var(--space-md);
}

.btn-lg {
  font-size: var(--font-size-lg);
  padding: var(--space-md) var(--space-xl);
}

/* أنواع الأزرار */
.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-secondary {
  background: var(--secondary-gradient);
  color: white;
}

.btn-success {
  background: var(--success-gradient);
  color: white;
}

.btn-info {
  background: var(--info-gradient);
  color: white;
}

.btn-warning {
  background: var(--warning-gradient);
  color: white;
}

.btn-danger {
  background: var(--danger-gradient);
  color: white;
}

/* أزرار النيون */
.btn-neon-blue {
  background-color: transparent;
  color: var(--neon-blue);
  border: 1px solid var(--neon-blue);
  box-shadow: var(--neon-blue-glow);
  text-shadow: 0 0 5px var(--neon-blue);
}

.btn-neon-blue:hover {
  background-color: rgba(0, 212, 255, 0.1);
  box-shadow: 0 0 10px var(--neon-blue), 0 0 30px rgba(0, 212, 255, 0.5);
}

.btn-neon-purple {
  background-color: transparent;
  color: var(--neon-purple);
  border: 1px solid var(--neon-purple);
  box-shadow: var(--neon-purple-glow);
  text-shadow: 0 0 5px var(--neon-purple);
}

.btn-neon-purple:hover {
  background-color: rgba(157, 0, 255, 0.1);
  box-shadow: 0 0 10px var(--neon-purple), 0 0 30px rgba(157, 0, 255, 0.5);
}

/* أزرار الزجاج */
.btn-glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  color: white;
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* أزرار النيومورفيزم */
.btn-neumorphism-light {
  background: var(--neumorphism-bg-light);
  box-shadow: var(--neumorphism-shadow-light);
  color: #666;
  border: none;
}

.btn-neumorphism-light:hover {
  box-shadow: var(--neumorphism-inset-light);
}

.btn-neumorphism-dark {
  background: var(--neumorphism-bg-dark);
  box-shadow: var(--neumorphism-shadow-dark);
  color: #eee;
  border: none;
}

.btn-neumorphism-dark:hover {
  box-shadow: var(--neumorphism-inset-dark);
}

/* أزرار الأيقونات */
.btn-icon {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon-sm {
  width: 2rem;
  height: 2rem;
  font-size: var(--font-size-sm);
}

.btn-icon-lg {
  width: 3rem;
  height: 3rem;
  font-size: var(--font-size-lg);
}

/* أزرار مع تأثيرات متقدمة */
.btn-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

.btn-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
  100% { transform: translateY(0px); }
}