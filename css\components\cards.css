/* أنماط البطاقات المحسنة */

/* البطاقة الأساسية */
.card {
  border: none;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  position: relative;
  z-index: 1;
}

/* بطاقة زجاجية */
.card-glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.card-glass:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px 0 rgba(31, 38, 135, 0.5);
}

.card-glass .card-header {
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
  padding: var(--space-md) var(--space-lg);
  font-weight: var(--font-weight-semibold);
}

.card-glass .card-body {
  padding: var(--space-lg);
  color: white;
}

.card-glass .card-footer {
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid var(--glass-border);
  padding: var(--space-md) var(--space-lg);
  color: white;
}

/* بطاقة نيومورفيزم */
.card-neumorphism-light {
  background: var(--neumorphism-bg-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--neumorphism-shadow-light);
  padding: var(--space-lg);
}

.card-neumorphism-light:hover {
  transform: translateY(-5px);
}

.card-neumorphism-dark {
  background: var(--neumorphism-bg-dark);
  border-radius: var(--radius-lg);
  box-shadow: var(--neumorphism-shadow-dark);
  padding: var(--space-lg);
  color: white;
}

.card-neumorphism-dark:hover {
  transform: translateY(-5px);
}

/* بطاقة نيون */
.card-neon {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  border: 1px solid var(--neon-blue);
  box-shadow: var(--neon-blue-glow);
  padding: var(--space-lg);
  color: white;
}

.card-neon:hover {
  box-shadow: 0 0 15px var(--neon-blue), 0 0 30px rgba(0, 212, 255, 0.5);
  transform: translateY(-10px);
}

.card-neon-purple {
  border-color: var(--neon-purple);
  box-shadow: var(--neon-purple-glow);
}

.card-neon-purple:hover {
  box-shadow: 0 0 15px var(--neon-purple), 0 0 30px rgba(157, 0, 255, 0.5);
}

.card-neon-pink {
  border-color: var(--neon-pink);
  box-shadow: var(--neon-pink-glow);
}

.card-neon-pink:hover {
  box-shadow: 0 0 15px var(--neon-pink), 0 0 30px rgba(255, 0, 128, 0.5);
}

/* بطاقة لوحة التحكم */
.dashboard-card {
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
  z-index: 1;
  height: 100%;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: -1;
}

.dashboard-card:hover::before {
  opacity: 0.1;
}

.dashboard-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px 0 rgba(31, 38, 135, 0.5);
}

.dashboard-card .card-body {
  padding: var(--space-xl) var(--space-lg);
  text-align: center;
  position: relative;
  z-index: 1;
  color: white;
}

.dashboard-card .icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.dashboard-card .title {
  margin-bottom: var(--space-sm);
  font-weight: var(--font-weight-semibold);
  color: white;
  font-size: var(--font-size-lg);
}

.dashboard-card .value {
  margin-bottom: var(--space-md);
  font-weight: var(--font-weight-bold);
  color: white;
  font-size: var(--font-size-3xl);
}

.dashboard-card .btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.dashboard-card .btn:hover {
  background: rgba(255, 255, 255, 0.3);
  color: white;
  transform: scale(1.05);
}

/* بطاقات مع تأثيرات متقدمة */
.card-3d {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.card-3d:hover {
  transform: rotateY(10deg) rotateX(10deg) translateY(-10px);
}

.card-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

/* بطاقة مع خلفية متدرجة متحركة */
.card-gradient-animated {
  background: linear-gradient(270deg, var(--primary-500), var(--secondary-500), var(--success-500), var(--info-500));
  background-size: 800% 800%;
  animation: gradient-animation 10s ease infinite;
}

@keyframes gradient-animation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}