<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام المحاسبة لوكالات السفريات</title>
    <link rel="stylesheet" href="css/style.new.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-plane-departure me-2"></i>
                نظام المحاسبة لوكالات السفريات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" id="sidebarToggle">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            الرئيسية
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> المدير
                        </a>
                        <ul class="dropdown-menu dropdown-menu-start glass">
                            <li><a class="dropdown-item text-white" href="#"><i class="fas fa-user me-2"></i> الملف الشخصي</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fas fa-cog me-2"></i> الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-white" href="index.html"><i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-lg-2 sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sales.html">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="purchases.html">
                            <i class="fas fa-shopping-bag"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reservations.html">
                            <i class="fas fa-calendar-check"></i> الحجوزات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="agents.html">
                            <i class="fas fa-user-tie"></i> الوكلاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.html">
                            <i class="fas fa-truck"></i> الموردين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.html">
                            <i class="fas fa-boxes"></i> المخزون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="finance.html">
                            <i class="fas fa-money-bill-wave"></i> المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.html">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-lg-10 main-content">
                <!-- عنوان الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="neon-text">لوحة التحكم</h2>
                    <div>
                        <span class="badge bg-primary me-2">
                            <i class="fas fa-calendar me-1"></i> 
                            <span id="currentDate">26 أغسطس 2023</span>
                        </span>
                        <span class="badge bg-info">
                            <i class="fas fa-clock me-1"></i> 
                            <span id="currentTime">10:30 صباحاً</span>
                        </span>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-4">
                        <div class="dashboard-card card-3d">
                            <div class="card-body">
                                <div class="icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <h5 class="title">إجمالي المبيعات</h5>
                                <h3 class="value">1,250,000 ريال</h3>
                                <a href="sales.html" class="btn btn-sm">عرض التفاصيل</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="dashboard-card card-3d">
                            <div class="card-body">
                                <div class="icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h5 class="title">العملاء</h5>
                                <h3 class="value">350</h3>
                                <a href="clients.html" class="btn btn-sm">عرض التفاصيل</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="dashboard-card card-3d">
                            <div class="card-body">
                                <div class="icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <h5 class="title">الحجوزات النشطة</h5>
                                <h3 class="value">75</h3>
                                <a href="reservations.html" class="btn btn-sm">عرض التفاصيل</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="dashboard-card card-3d">
                            <div class="card-body">
                                <div class="icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <h5 class="title">الأرباح</h5>
                                <h3 class="value">450,000 ريال</h3>
                                <a href="finance.html" class="btn btn-sm">عرض التفاصيل</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row mb-4">
                    <div class="col-md-8 mb-4">
                        <div class="card card-glass">
                            <div class="card-header">
                                <h5>المبيعات الشهرية</h5>
                            </div>
                            <div class="card-body">
                                <div style="height: 300px;">
                                    <canvas id="salesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card card-glass">
                            <div class="card-header">
                                <h5>توزيع الحجوزات</h5>
                            </div>
                            <div class="card-body">
                                <div style="height: 300px;">
                                    <canvas id="bookingDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أحدث الحجوزات والمهام -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card card-glass">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5>أحدث الحجوزات</h5>
                                <a href="reservations.html" class="btn btn-sm btn-neon-blue">عرض الكل</a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sortable">
                                        <thead>
                                            <tr>
                                                <th data-sort="id">#</th>
                                                <th data-sort="client">العميل</th>
                                                <th data-sort="service">الخدمة</th>
                                                <th data-sort="date">التاريخ</th>
                                                <th data-sort="status">الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td data-id="1001">#1001</td>
                                                <td data-client="أحمد محمد">أحمد محمد</td>
                                                <td data-service="تذكرة طيران">تذكرة طيران</td>
                                                <td data-date="2023-08-25">25/08/2023</td>
                                                <td data-status="مؤكد">
                                                    <span class="status-badge status-indicator-office">مؤكد</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td data-id="1002">#1002</td>
                                                <td data-client="سارة علي">سارة علي</td>
                                                <td data-service="حجز فندق">حجز فندق</td>
                                                <td data-date="2023-08-24">24/08/2023</td>
                                                <td data-status="قيد المعالجة">
                                                    <span class="status-badge status-processing">قيد المعالجة</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td data-id="1003">#1003</td>
                                                <td data-client="محمد خالد">محمد خالد</td>
                                                <td data-service="تأشيرة">تأشيرة</td>
                                                <td data-date="2023-08-23">23/08/2023</td>
                                                <td data-status="مكتمل">
                                                    <span class="status-badge status-delivered-indicator">مكتمل</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td data-id="1004">#1004</td>
                                                <td data-client="فاطمة أحمد">فاطمة أحمد</td>
                                                <td data-service="رحلة سياحية">رحلة سياحية</td>
                                                <td data-date="2023-08-22">22/08/2023</td>
                                                <td data-status="ملغي">
                                                    <span class="status-badge status-cancelled">ملغي</span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card card-glass">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5>المهام القادمة</h5>
                                <button class="btn btn-sm btn-neon-purple" data-bs-toggle="modal" data-bs-target="#addTaskModal">إضافة مهمة</button>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush bg-transparent">
                                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent border-bottom border-light">
                                        <div>
                                            <h6 class="mb-1">متابعة حجوزات الطيران</h6>
                                            <small class="text-white-50">اليوم - 12:00 مساءً</small>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">عاجل</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent border-bottom border-light">
                                        <div>
                                            <h6 class="mb-1">تحديث قائمة الأسعار</h6>
                                            <small class="text-white-50">غداً - 10:00 صباحاً</small>
                                        </div>
                                        <span class="badge bg-info rounded-pill">متوسط</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent border-bottom border-light">
                                        <div>
                                            <h6 class="mb-1">اجتماع مع الموردين</h6>
                                            <small class="text-white-50">28/08/2023 - 11:30 صباحاً</small>
                                        </div>
                                        <span class="badge bg-warning rounded-pill">مهم</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent border-bottom border-light">
                                        <div>
                                            <h6 class="mb-1">إعداد التقرير الشهري</h6>
                                            <small class="text-white-50">31/08/2023 - 02:00 مساءً</small>
                                        </div>
                                        <span class="badge bg-success rounded-pill">عادي</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مهمة جديدة -->
    <div class="modal fade" id="addTaskModal" tabindex="-1" aria-labelledby="addTaskModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTaskModalLabel">إضافة مهمة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm">
                        <div class="mb-3">
                            <label for="taskTitle" class="form-label">عنوان المهمة</label>
                            <input type="text" class="form-control" id="taskTitle" placeholder="أدخل عنوان المهمة" required>
                        </div>
                        <div class="mb-3">
                            <label for="taskDate" class="form-label">تاريخ المهمة</label>
                            <input type="date" class="form-control" id="taskDate" required>
                        </div>
                        <div class="mb-3">
                            <label for="taskTime" class="form-label">وقت المهمة</label>
                            <input type="time" class="form-control" id="taskTime" required>
                        </div>
                        <div class="mb-3">
                            <label for="taskPriority" class="form-label">الأولوية</label>
                            <select class="form-select" id="taskPriority" required>
                                <option value="">اختر الأولوية</option>
                                <option value="عاجل">عاجل</option>
                                <option value="مهم">مهم</option>
                                <option value="متوسط">متوسط</option>
                                <option value="عادي">عادي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="taskDescription" class="form-label">وصف المهمة</label>
                            <textarea class="form-control" id="taskDescription" rows="3" placeholder="أدخل وصف المهمة"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveTaskBtn">حفظ المهمة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- تأثير الجسيمات المتحركة -->
    <div class="particles" id="particles"></div>

    <!-- المكتبات والسكربتات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script type="module" src="js/main.new.js"></script>

    <script>
        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-SA', options);
            
            let hours = now.getHours();
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const ampm = hours >= 12 ? 'مساءً' : 'صباحاً';
            hours = hours % 12;
            hours = hours ? hours : 12;
            document.getElementById('currentTime').textContent = `${hours}:${minutes} ${ampm}`;
        }
        
        updateDateTime();
        setInterval(updateDateTime, 60000);
        
        // معالجة إضافة مهمة جديدة
        document.getElementById('saveTaskBtn').addEventListener('click', function() {
            const title = document.getElementById('taskTitle').value;
            const date = document.getElementById('taskDate').value;
            const time = document.getElementById('taskTime').value;
            const priority = document.getElementById('taskPriority').value;
            
            if (!title || !date || !time || !priority) {
                showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
                return;
            }
            
            // هنا سيتم إضافة منطق حفظ المهمة
            
            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addTaskModal'));
            modal.hide();
            
            // عرض رسالة نجاح
            showSuccessMessage('تم إضافة المهمة بنجاح');
            
            // إعادة تعيين النموذج
            document.getElementById('addTaskForm').reset();
        });
    </script>
</body>
</html>