<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العملاء - نظام المحاسبة لوكالات السفريات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">نظام المحاسبة لوكالات السفريات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> المدير
                        </a>
                        <ul class="dropdown-menu dropdown-menu-start">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-2 sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="clients.html">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reservations.html">
                            <i class="fas fa-calendar-check"></i> الحجوزات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="agents.html">
                            <i class="fas fa-user-tie"></i> الوكلاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.html">
                            <i class="fas fa-truck"></i> الموردون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.html">
                            <i class="fas fa-warehouse"></i> المخزون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sales.html">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="purchases.html">
                            <i class="fas fa-hand-holding-usd"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="accounts.html">
                            <i class="fas fa-file-invoice-dollar"></i> الحسابات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="finance.html">
                            <i class="fas fa-chart-line"></i> المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="templates.html">
                            <i class="fas fa-file-contract"></i> القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.html">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>العملاء</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClientModal">
                        <i class="fas fa-plus"></i> إضافة عميل جديد
                    </button>
                </div>

                <!-- فلترة البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form id="searchForm">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="searchName" class="form-label">اسم العميل</label>
                                    <input type="text" class="form-control" id="searchName" placeholder="ابحث باسم العميل">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchPassport" class="form-label">رقم الجواز</label>
                                    <input type="text" class="form-control" id="searchPassport" placeholder="ابحث برقم الجواز">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchStatus" class="form-label">حالة المعاملة</label>
                                    <select class="form-select" id="searchStatus">
                                        <option value="">الكل</option>
                                        <option value="processing">قيد تجهيز العميل</option>
                                        <option value="execution-office">قيد التنفيذ بالمكتب</option>
                                        <option value="execution-embassy">قيد التنفيذ بالسفارة</option>
                                        <option value="indicator-embassy">مؤشر في السفارة</option>
                                        <option value="indicator-office">مؤشر في المكتب</option>
                                        <option value="delivered-indicator">مسلم مؤشر للعميل</option>
                                        <option value="returned">مرجوع</option>
                                        <option value="cancelled">ملغي</option>
                                        <option value="withdrawn">مسحوب</option>
                                        <option value="note">ملاحظة</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="searchAgent" class="form-label">الوكيل</label>
                                    <select class="form-select" id="searchAgent">
                                        <option value="">الكل</option>
                                        <option value="1">وكيل 1</option>
                                        <option value="2">وكيل 2</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-redo"></i> إعادة تعيين
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول العملاء -->
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">قائمة العملاء</h5>
                        <div>
                            <button class="btn btn-sm btn-light">
                                <i class="fas fa-file-excel"></i> تصدير إكسل
                            </button>
                            <button class="btn btn-sm btn-light">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم العميل</th>
                                        <th>الاسم</th>
                                        <th>رقم الجواز</th>
                                        <th>رقم الجوال</th>
                                        <th>الوكيل</th>
                                        <th>نوع التأشيرة</th>
                                        <th>حالة المعاملة</th>
                                        <th>تاريخ التسليم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>#1001</td>
                                        <td>أحمد محمد</td>
                                        <td>A12345678</td>
                                        <td>771234567</td>
                                        <td>وكيل 1</td>
                                        <td>تأشيرة عمل فردي</td>
                                        <td><span class="status-badge status-execution-office">قيد التنفيذ بالمكتب</span></td>
                                        <td>2023-06-15</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#1002</td>
                                        <td>علي عبدالله</td>
                                        <td>B87654321</td>
                                        <td>772345678</td>
                                        <td>وكيل 2</td>
                                        <td>تأشيرة زيارة عائلية</td>
                                        <td><span class="status-badge status-indicator-embassy">مؤشر في السفارة</span></td>
                                        <td>2023-06-20</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#1003</td>
                                        <td>خالد سالم</td>
                                        <td>C11223344</td>
                                        <td>773456789</td>
                                        <td>وكيل 1</td>
                                        <td>تأشيرة عمل عادي</td>
                                        <td><span class="status-badge status-delivered-indicator">مسلم مؤشر للعميل</span></td>
                                        <td>2023-05-30</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#1004</td>
                                        <td>محمد إبراهيم</td>
                                        <td>D55667788</td>
                                        <td>774567890</td>
                                        <td>وكيل 2</td>
                                        <td>تأشيرة عمل فردي</td>
                                        <td><span class="status-badge status-returned">مرجوع</span></td>
                                        <td>-</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#1005</td>
                                        <td>عمر خالد</td>
                                        <td>E99887766</td>
                                        <td>775678901</td>
                                        <td>وكيل 1</td>
                                        <td>أخرى</td>
                                        <td><span class="status-badge status-processing">قيد تجهيز العميل</span></td>
                                        <td>2023-07-01</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- ترقيم الصفحات -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">السابق</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">التالي</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة عميل جديد -->
    <div class="modal fade" id="addClientModal" tabindex="-1" aria-labelledby="addClientModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addClientModalLabel">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addClientForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="clientName" class="form-label">الاسم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="clientName" required>
                            </div>
                            <div class="col-md-6">
                                <label for="passportNumber" class="form-label">رقم الجواز <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="passportNumber" required>
                            </div>
                            <div class="col-md-6">
                                <label for="phoneNumber" class="form-label">رقم الجوال <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="phoneNumber" required>
                            </div>
                            <div class="col-md-6">
                                <label for="agent" class="form-label">الوكيل <span class="text-danger">*</span></label>
                                <select class="form-select" id="agent" required>
                                    <option value="">اختر الوكيل</option>
                                    <option value="1">وكيل 1</option>
                                    <option value="2">وكيل 2</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="officeName" class="form-label">اسم مكتب التفويض</label>
                                <input type="text" class="form-control" id="officeName">
                            </div>
                            <div class="col-md-6">
                                <label for="orderNumber" class="form-label">رقم الطلب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="orderNumber" required>
                            </div>
                            <div class="col-md-6">
                                <label for="visaType" class="form-label">نوع التأشيرة <span class="text-danger">*</span></label>
                                <select class="form-select" id="visaType" required>
                                    <option value="">اختر نوع التأشيرة</option>
                                    <option value="individual">تأشيرة عمل فردي</option>
                                    <option value="normal">تأشيرة عمل عادي</option>
                                    <option value="family">تأشيرة زيارة عائلية</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="issuedNumber" class="form-label">رقم الصادر</label>
                                <input type="text" class="form-control" id="issuedNumber">
                            </div>
                            <div class="col-md-6">
                                <label for="recordNumber" class="form-label">رقم السجل</label>
                                <input type="text" class="form-control" id="recordNumber">
                            </div>
                            <div class="col-md-6">
                                <label for="companyName" class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" id="companyName">
                            </div>
                            <div class="col-md-6">
                                <label for="currency" class="form-label">نوع العملة <span class="text-danger">*</span></label>
                                <select class="form-select" id="currency" required>
                                    <option value="">اختر العملة</option>
                                    <option value="yer">ريال يمني</option>
                                    <option value="sar">ريال سعودي</option>
                                    <option value="usd">دولار أمريكي</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="totalAmount" class="form-label">اجمالي مبلغ المعاملة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="totalAmount" required>
                            </div>
                            <div class="col-md-6">
                                <label for="paidAmount" class="form-label">المبلغ المدفوع <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="paidAmount" required>
                            </div>
                            <div class="col-md-6">
                                <label for="totalVisaAmount" class="form-label">اجمالي مبلغ الفيزة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="totalVisaAmount" required>
                            </div>
                            <div class="col-md-6">
                                <label for="paidVisaAmount" class="form-label">المدفوع للفيزة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="paidVisaAmount" required>
                            </div>
                            <div class="col-md-6">
                                <label for="transactionStatus" class="form-label">حالة المعاملة <span class="text-danger">*</span></label>
                                <select class="form-select" id="transactionStatus" required>
                                    <option value="">اختر الحالة</option>
                                    <option value="processing">قيد تجهيز العميل</option>
                                    <option value="execution-office">قيد التنفيذ بالمكتب</option>
                                    <option value="execution-embassy">قيد التنفيذ بالسفارة</option>
                                    <option value="indicator-embassy">مؤشر في السفارة</option>
                                    <option value="indicator-office">مؤشر في المكتب</option>
                                    <option value="delivered-indicator">مسلم مؤشر للعميل</option>
                                    <option value="returned">مرجوع</option>
                                    <option value="cancelled">ملغي</option>
                                    <option value="withdrawn">مسحوب</option>
                                    <option value="note">ملاحظة</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="deliveryStatus" class="form-label">حالة التسليم</label>
                                <select class="form-select" id="deliveryStatus">
                                    <option value="">اختر الحالة</option>
                                    <option value="pending">قيد الانتظار</option>
                                    <option value="delivered">تم التسليم</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="deliveryDate" class="form-label">تاريخ تسليم المعاملة</label>
                                <input type="date" class="form-control" id="deliveryDate">
                            </div>
                            <div class="col-md-6">
                                <label for="transferDate" class="form-label">تاريخ الترحيل</label>
                                <input type="date" class="form-control" id="transferDate">
                            </div>
                            <div class="col-md-6">
                                <label for="visaDate" class="form-label">تاريخ التأشيرة</label>
                                <input type="date" class="form-control" id="visaDate">
                            </div>
                            <div class="col-md-6">
                                <label for="indicatorDeliveryDate" class="form-label">تاريخ التسليم للعميل مؤشر</label>
                                <input type="date" class="form-control" id="indicatorDeliveryDate">
                            </div>
                            <div class="col-md-6">
                                <label for="deliveryLocation" class="form-label">مكان التسليم</label>
                                <input type="text" class="form-control" id="deliveryLocation">
                            </div>
                            <div class="col-md-6">
                                <label for="attachments" class="form-label">المرفقات</label>
                                <input type="file" class="form-control" id="attachments" multiple>
                            </div>
                            <div class="col-12">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveClientBtn">حفظ العميل</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // حفظ العميل الجديد
            const saveClientBtn = document.getElementById('saveClientBtn');
            if (saveClientBtn) {
                saveClientBtn.addEventListener('click', function() {
                    const form = document.getElementById('addClientForm');
                    if (form.checkValidity()) {
                        // هنا سيتم إضافة منطق حفظ العميل
                        console.log('حفظ العميل الجديد');

                        // إغلاق النافذة
                        const modal = bootstrap.Modal.getInstance(document.getElementById('addClientModal'));
                        modal.hide();

                        // إعادة تعيين النموذج
                        form.reset();

                        // عرض رسالة نجاح
                        showSuccessMessage('تم حفظ العميل بنجاح');
                    } else {
                        form.reportValidity();
                    }
                });
            }

            // معالجة نموذج البحث
            const searchForm = document.getElementById('searchForm');
            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    // هنا سيتم إضافة منطق البحث
                    console.log('بحث عن العملاء');
                });
            }
        });
    </script>
</body>
</html>