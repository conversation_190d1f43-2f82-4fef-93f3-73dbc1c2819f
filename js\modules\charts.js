/**
 * وحدة الرسوم البيانية
 * تحتوي على دوال لإنشاء وتهيئة الرسوم البيانية المختلفة باستخدام Chart.js
 */

// تهيئة الألوان والأنماط المشتركة
const chartColors = {
  primary: 'rgba(102, 126, 234, 1)',
  primaryTransparent: 'rgba(102, 126, 234, 0.2)',
  secondary: 'rgba(118, 75, 162, 1)',
  secondaryTransparent: 'rgba(118, 75, 162, 0.2)',
  success: 'rgba(79, 172, 254, 1)',
  successTransparent: 'rgba(79, 172, 254, 0.2)',
  info: 'rgba(67, 233, 123, 1)',
  infoTransparent: 'rgba(67, 233, 123, 0.2)',
  warning: 'rgba(250, 112, 154, 1)',
  warningTransparent: 'rgba(250, 112, 154, 0.2)',
  danger: 'rgba(255, 106, 0, 1)',
  dangerTransparent: 'rgba(255, 106, 0, 0.2)',
};

// الإعدادات العامة للرسوم البيانية
const commonOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      labels: {
        color: 'white',
        font: {
          family: "'Tajawal', 'Segoe UI', 'Tahoma', 'Arial', sans-serif",
          size: 14
        }
      }
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      titleColor: 'white',
      bodyColor: 'white',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      borderWidth: 1,
      padding: 10,
      bodyFont: {
        family: "'Tajawal', 'Segoe UI', 'Tahoma', 'Arial', sans-serif",
        size: 14
      },
      titleFont: {
        family: "'Tajawal', 'Segoe UI', 'Tahoma', 'Arial', sans-serif",
        size: 16,
        weight: 'bold'
      }
    }
  },
  scales: {
    x: {
      grid: {
        color: 'rgba(255, 255, 255, 0.1)'
      },
      ticks: {
        color: 'rgba(255, 255, 255, 0.7)',
        font: {
          family: "'Tajawal', 'Segoe UI', 'Tahoma', 'Arial', sans-serif",
          size: 12
        }
      }
    },
    y: {
      grid: {
        color: 'rgba(255, 255, 255, 0.1)'
      },
      ticks: {
        color: 'rgba(255, 255, 255, 0.7)',
        font: {
          family: "'Tajawal', 'Segoe UI', 'Tahoma', 'Arial', sans-serif",
          size: 12
        }
      }
    }
  },
  animation: {
    duration: 2000,
    easing: 'easeOutQuart'
  }
};

// إنشاء رسم بياني خطي
function createLineChart(canvasId, data, options = {}) {
  const canvas = document.getElementById(canvasId);
  if (!canvas) return null;
  
  const ctx = canvas.getContext('2d');
  
  // دمج الخيارات المخصصة مع الخيارات العامة
  const chartOptions = {
    ...commonOptions,
    ...options
  };
  
  // إنشاء الرسم البياني
  return new Chart(ctx, {
    type: 'line',
    data: data,
    options: chartOptions
  });
}

// إنشاء رسم بياني شريطي
function createBarChart(canvasId, data, options = {}) {
  const canvas = document.getElementById(canvasId);
  if (!canvas) return null;
  
  const ctx = canvas.getContext('2d');
  
  // دمج الخيارات المخصصة مع الخيارات العامة
  const chartOptions = {
    ...commonOptions,
    ...options
  };
  
  // إنشاء الرسم البياني
  return new Chart(ctx, {
    type: 'bar',
    data: data,
    options: chartOptions
  });
}

// إنشاء رسم بياني دائري
function createPieChart(canvasId, data, options = {}) {
  const canvas = document.getElementById(canvasId);
  if (!canvas) return null;
  
  const ctx = canvas.getContext('2d');
  
  // دمج الخيارات المخصصة مع الخيارات العامة
  const chartOptions = {
    ...commonOptions,
    ...options
  };
  
  // إنشاء الرسم البياني
  return new Chart(ctx, {
    type: 'pie',
    data: data,
    options: chartOptions
  });
}

// إنشاء رسم بياني دائري مع فتحة في المنتصف
function createDoughnutChart(canvasId, data, options = {}) {
  const canvas = document.getElementById(canvasId);
  if (!canvas) return null;
  
  const ctx = canvas.getContext('2d');
  
  // دمج الخيارات المخصصة مع الخيارات العامة
  const chartOptions = {
    ...commonOptions,
    ...options,
    cutout: options.cutout || '70%'
  };
  
  // إنشاء الرسم البياني
  return new Chart(ctx, {
    type: 'doughnut',
    data: data,
    options: chartOptions
  });
}

// إنشاء رسم بياني مساحي
function createAreaChart(canvasId, data, options = {}) {
  const canvas = document.getElementById(canvasId);
  if (!canvas) return null;
  
  const ctx = canvas.getContext('2d');
  
  // تعديل البيانات لإضافة المساحة تحت الخط
  const areaData = {
    ...data,
    datasets: data.datasets.map(dataset => ({
      ...dataset,
      fill: true
    }))
  };
  
  // دمج الخيارات المخصصة مع الخيارات العامة
  const chartOptions = {
    ...commonOptions,
    ...options
  };
  
  // إنشاء الرسم البياني
  return new Chart(ctx, {
    type: 'line',
    data: areaData,
    options: chartOptions
  });
}

// إنشاء رسم بياني راداري
function createRadarChart(canvasId, data, options = {}) {
  const canvas = document.getElementById(canvasId);
  if (!canvas) return null;
  
  const ctx = canvas.getContext('2d');
  
  // دمج الخيارات المخصصة مع الخيارات العامة
  const chartOptions = {
    ...commonOptions,
    ...options,
    scales: {
      r: {
        angleLines: {
          color: 'rgba(255, 255, 255, 0.1)'
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)'
        },
        pointLabels: {
          color: 'rgba(255, 255, 255, 0.7)',
          font: {
            family: "'Tajawal', 'Segoe UI', 'Tahoma', 'Arial', sans-serif",
            size: 12
          }
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.7)',
          backdropColor: 'transparent'
        }
      }
    }
  };
  
  // إنشاء الرسم البياني
  return new Chart(ctx, {
    type: 'radar',
    data: data,
    options: chartOptions
  });
}

// إنشاء بيانات المبيعات الشهرية
function createMonthlySalesData() {
  return {
    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
    datasets: [
      {
        label: 'المبيعات',
        data: [12500, 19200, 15700, 18600, 23100, 26400, 28900, 31200, 29800, 27500, 32100, 35600],
        borderColor: chartColors.primary,
        backgroundColor: chartColors.primaryTransparent,
        tension: 0.4,
        borderWidth: 2,
        pointBackgroundColor: chartColors.primary,
        pointBorderColor: 'white',
        pointRadius: 4,
        pointHoverRadius: 6
      },
      {
        label: 'المصروفات',
        data: [9800, 12500, 11200, 13400, 16800, 19200, 21500, 23800, 22100, 20400, 24600, 27200],
        borderColor: chartColors.secondary,
        backgroundColor: chartColors.secondaryTransparent,
        tension: 0.4,
        borderWidth: 2,
        pointBackgroundColor: chartColors.secondary,
        pointBorderColor: 'white',
        pointRadius: 4,
        pointHoverRadius: 6
      }
    ]
  };
}

// إنشاء بيانات توزيع الحجوزات
function createBookingDistributionData() {
  return {
    labels: ['تذاكر طيران', 'حجوزات فنادق', 'تأشيرات', 'رحلات سياحية', 'خدمات أخرى'],
    datasets: [
      {
        data: [45, 25, 15, 10, 5],
        backgroundColor: [
          chartColors.primary,
          chartColors.secondary,
          chartColors.success,
          chartColors.warning,
          chartColors.info
        ],
        borderColor: 'rgba(255, 255, 255, 0.5)',
        borderWidth: 2,
        hoverOffset: 10
      }
    ]
  };
}

// تصدير الدوال والمتغيرات
export {
  chartColors,
  commonOptions,
  createLineChart,
  createBarChart,
  createPieChart,
  createDoughnutChart,
  createAreaChart,
  createRadarChart,
  createMonthlySalesData,
  createBookingDistributionData
};