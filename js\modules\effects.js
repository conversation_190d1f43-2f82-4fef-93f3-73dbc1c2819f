/**
 * وحدة التأثيرات المتقدمة
 * تحتوي على دوال لإضافة تأثيرات بصرية وحركية متقدمة للواجهة
 */

// تهيئة تأثير الجسيمات المتحركة
function initParticles() {
  const particlesContainer = document.getElementById('particles');
  if (!particlesContainer) return;
  
  // إزالة الجسيمات الموجودة
  particlesContainer.innerHTML = '';
  
  // إنشاء جسيمات جديدة
  const particleCount = 20;
  
  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div');
    particle.className = 'particle';
    
    // تعيين حجم عشوائي
    const size = Math.random() * 15 + 5;
    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;
    
    // تعيين موقع أفقي عشوائي
    const posX = Math.random() * 100;
    particle.style.left = `${posX}%`;
    
    // تعيين موقع رأسي عشوائي خارج الشاشة
    const posY = Math.random() * 100 + 100;
    particle.style.bottom = `${-posY}%`;
    
    // تعيين تأخير عشوائي للحركة
    const delay = Math.random() * 5;
    particle.style.animationDelay = `${delay}s`;
    
    // تعيين مدة عشوائية للحركة
    const duration = Math.random() * 10 + 10;
    particle.style.animationDuration = `${duration}s`;
    
    // تعيين شفافية عشوائية
    const opacity = Math.random() * 0.5 + 0.1;
    particle.style.opacity = opacity;
    
    // إضافة الجسيم إلى الحاوية
    particlesContainer.appendChild(particle);
  }
}

// تهيئة تأثير الزجاج المتقدم
function initGlassmorphism() {
  const glassElements = document.querySelectorAll('.glass, .card-glass, .btn-glass');
  
  glassElements.forEach(element => {
    // إضافة تأثير التموج عند تحريك الماوس
    element.addEventListener('mousemove', (e) => {
      const rect = element.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      
      const angleX = (y - centerY) / 10;
      const angleY = (centerX - x) / 10;
      
      element.style.transform = `perspective(1000px) rotateX(${angleX}deg) rotateY(${angleY}deg)`;
      
      // إضافة تأثير الانعكاس
      const shine = element.querySelector('.glass-shine');
      if (shine) {
        shine.style.opacity = '0.2';
        shine.style.transform = `translate(${x}px, ${y}px)`;
      } else {
        const newShine = document.createElement('div');
        newShine.className = 'glass-shine';
        newShine.style.position = 'absolute';
        newShine.style.top = '0';
        newShine.style.left = '0';
        newShine.style.width = '100px';
        newShine.style.height = '100px';
        newShine.style.borderRadius = '50%';
        newShine.style.background = 'radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%)';
        newShine.style.transform = `translate(${x}px, ${y}px)`;
        newShine.style.pointerEvents = 'none';
        newShine.style.opacity = '0.2';
        newShine.style.transition = 'opacity 0.3s ease';
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(newShine);
      }
    });
    
    // إعادة تعيين التأثير عند مغادرة الماوس
    element.addEventListener('mouseleave', () => {
      element.style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
      
      const shine = element.querySelector('.glass-shine');
      if (shine) {
        shine.style.opacity = '0';
      }
    });
  });
}

// تهيئة تأثير النيون المتقدم
function initNeonEffect() {
  const neonElements = document.querySelectorAll('.neon-text, .neon-border, .btn-neon-blue, .btn-neon-purple, .card-neon');
  
  neonElements.forEach(element => {
    // إضافة تأثير النبض للعناصر النيون
    element.classList.add('pulse-animation');
    
    // تعزيز تأثير النيون عند التحويم
    element.addEventListener('mouseenter', () => {
      const color = window.getComputedStyle(element).getPropertyValue('color');
      const borderColor = window.getComputedStyle(element).getPropertyValue('border-color');
      
      // تعزيز توهج النص
      if (element.classList.contains('neon-text')) {
        element.style.textShadow = `0 0 5px ${color}, 0 0 10px ${color}, 0 0 20px ${color}, 0 0 30px ${color}`;
      }
      
      // تعزيز توهج الحدود
      if (element.classList.contains('neon-border') || element.classList.contains('btn-neon-blue') || 
          element.classList.contains('btn-neon-purple') || element.classList.contains('card-neon')) {
        element.style.boxShadow = `0 0 5px ${borderColor}, 0 0 10px ${borderColor}, 0 0 20px ${borderColor}, 0 0 30px ${borderColor}`;
      }
    });
    
    // إعادة تعيين التأثير عند مغادرة الماوس
    element.addEventListener('mouseleave', () => {
      if (element.classList.contains('neon-text')) {
        element.style.textShadow = '';
      }
      
      if (element.classList.contains('neon-border') || element.classList.contains('btn-neon-blue') || 
          element.classList.contains('btn-neon-purple') || element.classList.contains('card-neon')) {
        element.style.boxShadow = '';
      }
    });
  });
}

// تهيئة تأثير البطاقات ثلاثية الأبعاد
function init3DCards() {
  const cards = document.querySelectorAll('.card-3d');
  
  cards.forEach(card => {
    card.addEventListener('mousemove', (e) => {
      const rect = card.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      
      const angleX = (y - centerY) / 15;
      const angleY = (centerX - x) / 15;
      
      card.style.transform = `perspective(1000px) rotateX(${angleX}deg) rotateY(${angleY}deg) translateZ(10px)`;
    });
    
    card.addEventListener('mouseleave', () => {
      card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
    });
  });
}

// تهيئة جميع التأثيرات
function initAllEffects() {
  // تهيئة تأثير الجسيمات
  initParticles();
  
  // تهيئة تأثير الزجاج المتقدم
  initGlassmorphism();
  
  // تهيئة تأثير النيون
  initNeonEffect();
  
  // تهيئة البطاقات ثلاثية الأبعاد
  init3DCards();
  
  // إعادة تهيئة الجسيمات عند تغيير حجم النافذة
  window.addEventListener('resize', initParticles);
}

// تصدير الدوال
export {
  initParticles,
  initGlassmorphism,
  initNeonEffect,
  init3DCards,
  initAllEffects
};