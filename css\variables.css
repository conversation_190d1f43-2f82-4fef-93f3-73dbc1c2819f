/* نظام التصميم الموحد - متغيرات CSS */
:root {
  /* نظام الألوان الأساسي - محسن */
  --primary-100: #e0e8ff;
  --primary-200: #c0d1ff;
  --primary-300: #a1baff;
  --primary-400: #81a3ff;
  --primary-500: #667eea;
  --primary-600: #5a6fd1;
  --primary-700: #4e60b8;
  --primary-800: #42529f;
  --primary-900: #364386;
  
  /* نظام الألوان الثانوي - محسن */
  --secondary-100: #f5e0ff;
  --secondary-200: #ebc0ff;
  --secondary-300: #e1a1ff;
  --secondary-400: #d781ff;
  --secondary-500: #764ba2;
  --secondary-600: #6a4292;
  --secondary-700: #5e3982;
  --secondary-800: #523072;
  --secondary-900: #462762;
  
  /* ألوان الحالة */
  --success-100: #e0fff4;
  --success-500: #4facfe;
  --success-900: #0072c6;
  
  --warning-100: #fff8e0;
  --warning-500: #fa709a;
  --warning-900: #c62368;
  
  --danger-100: #ffe0e0;
  --danger-500: #ff6a00;
  --danger-900: #c62200;
  
  --info-100: #e0f8ff;
  --info-500: #43e97b;
  --info-900: #00a843;
  
  /* ألوان النيون المحسنة */
  --neon-blue: #00d4ff;
  --neon-blue-glow: 0 0 5px rgba(0, 212, 255, 0.7), 0 0 20px rgba(0, 212, 255, 0.5);
  
  --neon-purple: #9d00ff;
  --neon-purple-glow: 0 0 5px rgba(157, 0, 255, 0.7), 0 0 20px rgba(157, 0, 255, 0.5);
  
  --neon-pink: #ff0080;
  --neon-pink-glow: 0 0 5px rgba(255, 0, 128, 0.7), 0 0 20px rgba(255, 0, 128, 0.5);
  
  --neon-green: #39ff14;
  --neon-green-glow: 0 0 5px rgba(57, 255, 20, 0.7), 0 0 20px rgba(57, 255, 20, 0.5);
  
  /* التدرجات اللونية المحسنة */
  --primary-gradient: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --info-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --danger-gradient: linear-gradient(135deg, #ff6a00 0%, #ee0979 100%);
  --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);
  
  /* متغيرات الزجاج (Glassmorphism) المحسنة */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-bg-dark: rgba(0, 0, 0, 0.2);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-border-dark: rgba(255, 255, 255, 0.05);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-blur: blur(10px);
  
  /* متغيرات النيومورفيزم (Neumorphism) المحسنة */
  --neumorphism-bg-light: #e0e5ec;
  --neumorphism-bg-dark: #2d2d2d;
  --neumorphism-shadow-light: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
  --neumorphism-shadow-dark: 8px 8px 16px #2d2d2d, -8px -8px 16px #4a4a4a;
  --neumorphism-inset-light: inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff;
  --neumorphism-inset-dark: inset 8px 8px 16px #2d2d2d, inset -8px -8px 16px #4a4a4a;
  
  /* نظام التباعد */
  --space-xs: 0.25rem;  /* 4px */
  --space-sm: 0.5rem;   /* 8px */
  --space-md: 1rem;     /* 16px */
  --space-lg: 1.5rem;   /* 24px */
  --space-xl: 2rem;     /* 32px */
  --space-2xl: 3rem;    /* 48px */
  --space-3xl: 4rem;    /* 64px */
  
  /* نظام الخطوط */
  --font-family-primary: 'Tajawal', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  --font-family-heading: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  
  /* أحجام الخطوط */
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-md: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  
  /* أوزان الخطوط */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* نصف قطر الزوايا */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.5rem;   /* 8px */
  --radius-lg: 1rem;     /* 16px */
  --radius-xl: 1.5rem;   /* 24px */
  --radius-2xl: 2rem;    /* 32px */
  --radius-full: 9999px;
  
  /* الظلال */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* متغيرات الانتقالات */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms ease;
  --transition-slow: 500ms ease;
  
  /* متغيرات الرسوم المتحركة */
  --animation-duration-fast: 0.3s;
  --animation-duration-normal: 0.5s;
  --animation-duration-slow: 1s;
}