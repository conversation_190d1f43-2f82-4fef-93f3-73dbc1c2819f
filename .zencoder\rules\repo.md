---
description: Repository Information Overview
alwaysApply: true
---

# Travel Agency Accounting System Information

## Summary
This is a web-based accounting system for travel agencies in Yemen. The application provides a comprehensive interface for managing travel agency operations including sales, purchases, reservations, inventory, clients, agents, suppliers, finance, and reporting.

## Structure
- **css/**: Contains styling files for the application
- **js/**: Contains JavaScript files for client-side functionality
- **html files**: Multiple HTML pages for different sections of the application

## Language & Runtime
**Language**: HTML, CSS, JavaScript
**Frontend Framework**: Bootstrap 5.3.0
**Icons**: Font Awesome 6.4.0

## Dependencies
**Main Dependencies**:
- Bootstrap 5.3.0 (CDN)
- Font Awesome 6.4.0 (CDN)

## Application Structure
**Main Pages**:
- `index.html`: Login and registration page
- `dashboard.html`: Main dashboard with overview statistics
- `sales.html`: Sales management
- `purchases.html`: Purchase management
- `reservations.html`: Reservation management
- `inventory.html`: Inventory management
- `clients.html`: Client management
- `agents.html`: Agent management
- `suppliers.html`: Supplier management
- `finance.html`: Financial management
- `reports.html`: Reporting system
- `accounts.html`: Account management
- `settings.html`: System settings
- `templates.html`: Document templates

## Frontend Features
**UI Components**:
- Glassmorphism design with blur effects
- Neumorphism elements for depth
- Neon effects for highlights
- Responsive layout for all device sizes
- RTL (Right-to-Left) layout for Arabic language
- Animated elements (gradients, floating cards)

**JavaScript Functionality**:
- Form validation for login and registration
- Alert and notification system
- Confirmation dialogs for critical actions
- Authentication flow (currently client-side only)

## Design System
**Color Scheme**:
- Primary gradient: Linear gradient from #667eea to #764ba2
- Secondary gradient: Linear gradient from #f093fb to #f5576c
- Success gradient: Linear gradient from #4facfe to #00f2fe
- Warning gradient: Linear gradient from #fa709a to #fee140
- Danger gradient: Linear gradient from #ff6a00 to #ee0979

**UI Elements**:
- Glass-effect cards and containers
- Neon text and borders for emphasis
- Neumorphic buttons and inputs
- Status badges for different states
- Custom form controls