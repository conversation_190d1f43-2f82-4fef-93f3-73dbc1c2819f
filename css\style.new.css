/* CSS للنظام المحاسبي لوكالات السفريات - تصميم حديث متطور */

/* استيراد ملفات CSS الأخرى */
@import url('variables.css');
@import url('components/buttons.css');
@import url('components/cards.css');
@import url('components/forms.css');

/* استيراد الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@400;600;700&display=swap');

/* إعدادات عامة */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
  background-size: 400% 400%;
  animation: gradientBG 15s ease infinite;
  color: white;
  min-height: 100vh;
  overflow-x: hidden;
  line-height: 1.6;
}

@keyframes gradientBG {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* تأثير الزجاج (Glassmorphism) */
.glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

/* تأثير النيون (Neon) */
.neon-text {
  color: var(--neon-blue);
  text-shadow: var(--neon-blue-glow);
}

.neon-border {
  box-shadow: var(--neon-blue-glow);
  border: 1px solid var(--neon-blue);
}

/* تأثير النيومورفيزم (Neumorphism) */
.neumorphism-light {
  background: var(--neumorphism-bg-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--neumorphism-shadow-light);
}

.neumorphism-dark {
  background: var(--neumorphism-bg-dark);
  border-radius: var(--radius-lg);
  box-shadow: var(--neumorphism-shadow-dark);
}

/* شريط التنقل */
.navbar {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  padding: var(--space-md) var(--space-lg);
}

.navbar-brand {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 0 15px rgba(102, 126, 234, 0.7);
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: var(--space-sm) var(--space-md);
  margin: 0 var(--space-xs);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.navbar-nav .nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.navbar-nav .nav-link.active {
  color: white;
  background: rgba(255, 255, 255, 0.2);
}

/* الشريط الجانبي */
.sidebar {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-left: 1px solid var(--glass-border);
  min-height: calc(100vh - 56px);
  padding: var(--space-lg) 0;
  box-shadow: 8px 0 32px 0 rgba(31, 38, 135, 0.37);
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: var(--space-md) var(--space-xl);
  margin: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  z-index: 1;
  display: flex;
  align-items: center;
}

.sidebar .nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: var(--primary-gradient);
  transition: width var(--transition-normal);
  z-index: -1;
  border-radius: var(--radius-lg);
}

.sidebar .nav-link:hover::before {
  width: 100%;
}

.sidebar .nav-link:hover {
  color: white;
  transform: translateX(5px);
}

.sidebar .nav-link.active {
  color: white;
  background: var(--primary-gradient);
  box-shadow: 0 0 15px rgba(102, 126, 234, 0.7);
}

.sidebar .nav-link i, .sidebar .nav-link svg {
  margin-left: var(--space-md);
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
  padding: var(--space-xl);
}

/* الجداول */
.table {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: var(--radius-lg);
  overflow: hidden;
  color: white;
  margin-bottom: var(--space-xl);
}

.table thead th {
  background: var(--primary-gradient);
  color: white;
  font-weight: var(--font-weight-semibold);
  border: none;
  padding: var(--space-md);
  white-space: nowrap;
}

.table tbody td {
  border-color: rgba(255, 255, 255, 0.1);
  padding: var(--space-md);
  vertical-align: middle;
}

.table tbody tr {
  transition: all var(--transition-normal);
}

.table tbody tr:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* شارات الحالة */
.status-badge {
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  display: inline-block;
}

.status-processing {
  background: rgba(255, 243, 205, 0.3);
  color: #fff3cd;
  border: 1px solid #fff3cd;
}

.status-execution-office {
  background: rgba(207, 226, 255, 0.3);
  color: #cfe2ff;
  border: 1px solid #cfe2ff;
}

.status-execution-embassy {
  background: rgba(226, 217, 243, 0.3);
  color: #e2d9f3;
  border: 1px solid #e2d9f3;
}

.status-indicator-embassy {
  background: rgba(248, 215, 218, 0.3);
  color: #f8d7da;
  border: 1px solid #f8d7da;
}

.status-indicator-office {
  background: rgba(209, 231, 221, 0.3);
  color: #d1e7dd;
  border: 1px solid #d1e7dd;
}

.status-delivered-indicator {
  background: rgba(207, 244, 252, 0.3);
  color: #cff4fc;
  border: 1px solid #cff4fc;
}

.status-returned {
  background: rgba(248, 215, 218, 0.3);
  color: #f8d7da;
  border: 1px solid #f8d7da;
}

.status-cancelled {
  background: rgba(226, 227, 229, 0.3);
  color: #e2e3e5;
  border: 1px solid #e2e3e5;
}

/* التبويبات */
.nav-tabs {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: var(--space-lg);
}

.nav-tabs .nav-link {
  color: rgba(255, 255, 255, 0.7);
  font-weight: var(--font-weight-medium);
  border: none;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  padding: var(--space-md) var(--space-lg);
  margin-right: var(--space-xs);
  transition: all var(--transition-normal);
}

.nav-tabs .nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.nav-tabs .nav-link.active {
  color: white;
  background: var(--primary-gradient);
  border: none;
}

/* النوافذ المنبثقة */
.modal-content {
  border-radius: var(--radius-lg);
  border: none;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  color: white;
}

.modal-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: var(--primary-gradient);
  color: white;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  padding: var(--space-md) var(--space-lg);
}

.modal-title {
  color: white;
  font-weight: var(--font-weight-semibold);
}

.modal-body {
  padding: var(--space-lg);
}

.modal-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: var(--space-md) var(--space-lg);
}

/* تأثيرات الحركة */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* تأثيرات الجسيمات المتحركة */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: float-up 10s infinite linear;
}

@keyframes float-up {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) scale(1);
    opacity: 0;
  }
}

/* تأثيرات الوميض */
@keyframes blink {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 1; }
}

.blink {
  animation: blink 2s infinite;
}

/* تصميم متجاوب */
@media (max-width: 992px) {
  .sidebar {
    position: fixed;
    top: 56px;
    right: -250px;
    width: 250px;
    z-index: 1030;
    transition: right var(--transition-normal);
  }
  
  .sidebar.show {
    right: 0;
  }
  
  .main-content {
    padding: var(--space-md);
  }
  
  .dashboard-card .icon {
    font-size: 2.5rem;
  }
  
  .dashboard-card .value {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 768px) {
  .table-responsive {
    border-radius: var(--radius-lg);
  }
  
  .modal-dialog {
    margin: var(--space-sm);
  }
  
  .dashboard-card .icon {
    font-size: 2rem;
  }
  
  .dashboard-card .value {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 576px) {
  .main-content {
    padding: var(--space-sm);
  }
  
  .card-body {
    padding: var(--space-md);
  }
  
  .btn {
    padding: var(--space-xs) var(--space-md);
  }
}