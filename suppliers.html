<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموردون - نظام المحاسبة لوكالات السفريات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">نظام المحاسبة لوكالات السفريات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> المدير
                        </a>
                        <ul class="dropdown-menu dropdown-menu-start">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-2 sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reservations.html">
                            <i class="fas fa-calendar-check"></i> الحجوزات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="agents.html">
                            <i class="fas fa-user-tie"></i> الوكلاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="suppliers.html">
                            <i class="fas fa-truck"></i> الموردون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.html">
                            <i class="fas fa-warehouse"></i> المخزون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sales.html">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="purchases.html">
                            <i class="fas fa-hand-holding-usd"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="accounts.html">
                            <i class="fas fa-file-invoice-dollar"></i> الحسابات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="finance.html">
                            <i class="fas fa-chart-line"></i> المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="templates.html">
                            <i class="fas fa-file-contract"></i> القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.html">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>الموردون</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                        <i class="fas fa-plus"></i> إضافة مورد جديد
                    </button>
                </div>

                <!-- فلترة البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form id="searchForm">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="searchName" class="form-label">اسم المورد</label>
                                    <input type="text" class="form-control" id="searchName" placeholder="ابحث باسم المورد">
                                </div>
                                <div class="col-md-4">
                                    <label for="searchCategory" class="form-label">الفئة</label>
                                    <select class="form-select" id="searchCategory">
                                        <option value="">الكل</option>
                                        <option value="transport">النقل</option>
                                        <option value="accommodation">الإقامة</option>
                                        <option value="food">الطعام</option>
                                        <option value="services">الخدمات</option>
                                        <option value="equipment">المعدات</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="searchStatus" class="form-label">الحالة</label>
                                    <select class="form-select" id="searchStatus">
                                        <option value="">الكل</option>
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-redo"></i> إعادة تعيين
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول الموردين -->
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">قائمة الموردين</h5>
                        <div>
                            <button class="btn btn-sm btn-light">
                                <i class="fas fa-file-excel"></i> تصدير إكسل
                            </button>
                            <button class="btn btn-sm btn-light">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم المورد</th>
                                        <th>الاسم</th>
                                        <th>الفئة</th>
                                        <th>رقم الجوال</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>العنوان</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>#5001</td>
                                        <td>شركة النقل السريع</td>
                                        <td>النقل</td>
                                        <td>771234567</td>
                                        <td><EMAIL></td>
                                        <td>صنعاء - شارع التحرير</td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#5002</td>
                                        <td>فنادق الراحة</td>
                                        <td>الإقامة</td>
                                        <td>772345678</td>
                                        <td><EMAIL></td>
                                        <td>عدن - كريتر</td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#5003</td>
                                        <td>مطاعم الشام</td>
                                        <td>الطعام</td>
                                        <td>773456789</td>
                                        <td><EMAIL></td>
                                        <td>تعز - شارع 26 سبتمبر</td>
                                        <td><span class="badge bg-danger">غير نشط</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مورد جديد -->
    <div class="modal fade" id="addSupplierModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">إضافة مورد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addSupplierForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="supplierName" class="form-label">اسم المورد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="supplierName" required>
                            </div>
                            <div class="col-md-6">
                                <label for="supplierCategory" class="form-label">الفئة <span class="text-danger">*</span></label>
                                <select class="form-select" id="supplierCategory" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="transport">النقل</option>
                                    <option value="accommodation">الإقامة</option>
                                    <option value="food">الطعام</option>
                                    <option value="services">الخدمات</option>
                                    <option value="equipment">المعدات</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="supplierPhone" class="form-label">رقم الجوال <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="supplierPhone" required>
                            </div>
                            <div class="col-md-6">
                                <label for="supplierEmail" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="supplierEmail">
                            </div>
                            <div class="col-12">
                                <label for="supplierAddress" class="form-label">العنوان</label>
                                <textarea class="form-control" id="supplierAddress" rows="2"></textarea>
                            </div>
                            <div class="col-md-6">
                                <label for="supplierContactPerson" class="form-label">شخص الاتصال</label>
                                <input type="text" class="form-control" id="supplierContactPerson">
                            </div>
                            <div class="col-md-6">
                                <label for="supplierStatus" class="form-label">الحالة</label>
                                <select class="form-select" id="supplierStatus">
                                    <option value="active" selected>نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="supplierNotes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="supplierNotes" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveSupplier()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        // دالة حفظ المورد
        function saveSupplier() {
            const form = document.getElementById('addSupplierForm');

            if (form.checkValidity()) {
                // هنا سيتم إضافة منطق حفظ المورد
                console.log('حفظ المورد');

                // إغلاق النافذة المنبثقة
                const modal = bootstrap.Modal.getInstance(document.getElementById('addSupplierModal'));
                modal.hide();

                // إعادة تعيين النموذج
                form.reset();

                // عرض رسالة نجاح
                showSuccessMessage('تم حفظ المورد بنجاح');
            } else {
                form.reportValidity();
            }
        }

        // دالة عرض حجوزات معينة
        function showReservations(type) {
            // تحديد التبويب المناسب حسب النوع
            const tabMap = {
                'umrah': 'umrah-tab',
                'hajj': 'hajj-tab',
                'passport': 'passport-tab',
                'bus': 'bus-tab',
                'car': 'car-tab',
                'flight': 'flight-tab',
                'document': 'document-tab'
            };

            const tabId = tabMap[type];
            if (tabId) {
                const tab = document.getElementById(tabId);
                if (tab) {
                    tab.click();
                }
            }
        }
    </script>
</body>
</html>