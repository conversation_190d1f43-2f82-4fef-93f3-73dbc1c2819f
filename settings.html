<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام المحاسبة لوكالات السفريات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">نظام المحاسبة لوكالات السفريات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> المدير
                        </a>
                        <ul class="dropdown-menu dropdown-menu-start">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-2 sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reservations.html">
                            <i class="fas fa-calendar-check"></i> الحجوزات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="agents.html">
                            <i class="fas fa-user-tie"></i> الوكلاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.html">
                            <i class="fas fa-truck"></i> الموردون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.html">
                            <i class="fas fa-warehouse"></i> المخزون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sales.html">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="purchases.html">
                            <i class="fas fa-hand-holding-usd"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="accounts.html">
                            <i class="fas fa-file-invoice-dollar"></i> الحسابات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="finance.html">
                            <i class="fas fa-chart-line"></i> المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="templates.html">
                            <i class="fas fa-file-contract"></i> القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="settings.html">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-10 main-content">
                <h2 class="mb-4">الإعدادات</h2>

                <!-- تبويبات الإعدادات -->
                <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">إعدادات عامة</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab" aria-controls="company" aria-selected="false">بيانات الشركة</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="false">المستخدمون والصلاحيات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab" aria-controls="backup" aria-selected="false">النسخ الاحتياطي</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab" aria-controls="notifications" aria-selected="false">الإشعارات</button>
                    </li>
                </ul>

                <div class="tab-content" id="settingsTabsContent">
                    <!-- تبويب الإعدادات العامة -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">الإعدادات العامة</h5>
                            </div>
                            <div class="card-body">
                                <form id="generalSettingsForm">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="systemName" class="form-label">اسم النظام</label>
                                            <input type="text" class="form-control" id="systemName" value="نظام المحاسبة لوكالات السفريات">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="systemLanguage" class="form-label">لغة النظام</label>
                                            <select class="form-select" id="systemLanguage">
                                                <option value="ar" selected>العربية</option>
                                                <option value="en">English</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                            <select class="form-select" id="timezone">
                                                <option value="Asia/Aden" selected>توقيت اليمن (GMT+3)</option>
                                                <option value="Asia/Riyadh">توقيت السعودية (GMT+3)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="dateFormat" class="form-label">تنسيق التاريخ</label>
                                            <select class="form-select" id="dateFormat">
                                                <option value="Y-m-d" selected>YYYY-MM-DD</option>
                                                <option value="d/m/Y">DD/MM/YYYY</option>
                                                <option value="m/d/Y">MM/DD/YYYY</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="currency" class="form-label">العملة الافتراضية</label>
                                            <select class="form-select" id="currency">
                                                <option value="YER" selected>ريال يمني (YER)</option>
                                                <option value="SAR">ريال سعودي (SAR)</option>
                                                <option value="USD">دولار أمريكي (USD)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="currencySymbol" class="form-label">رمز العملة</label>
                                            <select class="form-select" id="currencySymbol">
                                                <option value="after" selected>بعد الرقم (100 ريال)</option>
                                                <option value="before">قبل الرقم (ريال 100)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="itemsPerPage" class="form-label">عدد العناصر في الصفحة</label>
                                            <select class="form-select" id="itemsPerPage">
                                                <option value="10">10</option>
                                                <option value="25">25</option>
                                                <option value="50" selected>50</option>
                                                <option value="100">100</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="autoLogout" class="form-label">تسجيل الخروج التلقائي (بالدقائق)</label>
                                            <input type="number" class="form-control" id="autoLogout" value="30" min="5" max="120">
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                                <label class="form-check-label" for="maintenanceMode">
                                                    وضع الصيانة
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="debugMode" checked>
                                                <label class="form-check-label" for="debugMode">
                                                    وضع التصحيح
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save"></i> حفظ الإعدادات
                                            </button>
                                            <button type="reset" class="btn btn-secondary">
                                                <i class="fas fa-undo"></i> إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب بيانات الشركة -->
                    <div class="tab-pane fade" id="company" role="tabpanel" aria-labelledby="company-tab">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">بيانات الشركة</h5>
                            </div>
                            <div class="card-body">
                                <form id="companySettingsForm">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="companyName" class="form-label">اسم الشركة</label>
                                            <input type="text" class="form-control" id="companyName" value="وكالة السفريات اليمنية">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="commercialRegister" class="form-label">السجل التجاري</label>
                                            <input type="text" class="form-control" id="commercialRegister" value="12345">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="taxNumber" class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control" id="taxNumber" value="67890">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="phone" class="form-label">رقم الهاتف</label>
                                            <input type="text" class="form-control" id="phone" value="0123456789">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="email" class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" id="email" value="<EMAIL>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="website" class="form-label">الموقع الإلكتروني</label>
                                            <input type="text" class="form-control" id="website" value="www.example.com">
                                        </div>
                                        <div class="col-12">
                                            <label for="address" class="form-label">العنوان</label>
                                            <textarea class="form-control" id="address" rows="2">صنعاء - شارع التحرير</textarea>
                                        </div>
                                        <div class="col-12">
                                            <label for="logo" class="form-label">شعار الشركة</label>
                                            <input type="file" class="form-control" id="logo" accept="image/*">
                                            <div class="mt-2">
                                                <img src="https://via.placeholder.com/150" alt="شعار الشركة" class="img-thumbnail" style="max-width: 150px;">
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save"></i> حفظ البيانات
                                            </button>
                                            <button type="reset" class="btn btn-secondary">
                                                <i class="fas fa-undo"></i> إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب المستخدمين والصلاحيات -->
                    <div class="tab-pane fade" id="users" role="tabpanel" aria-labelledby="users-tab">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5>المستخدمون والصلاحيات</h5>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-plus"></i> إضافة مستخدم جديد
                            </button>
                        </div>

                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">قائمة المستخدمين</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>المستخدم</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>الدور</th>
                                                <th>الحالة</th>
                                                <th>تاريخ الإنشاء</th>
                                                <th>آخر تسجيل دخول</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle me-2">م</div>
                                                        <span>المدير</span>
                                                    </div>
                                                </td>
                                                <td><EMAIL></td>
                                                <td><span class="badge bg-danger">مدير النظام</span></td>
                                                <td><span class="badge bg-success">نشط</span></td>
                                                <td>2023-01-01</td>
                                                <td>2023-05-15 10:30</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle me-2">أ</div>
                                                        <span>أحمد محمد</span>
                                                    </div>
                                                </td>
                                                <td><EMAIL></td>
                                                <td><span class="badge bg-primary">محاسب</span></td>
                                                <td><span class="badge bg-success">نشط</span></td>
                                                <td>2023-02-15</td>
                                                <td>2023-05-14 15:45</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle me-2">ع</div>
                                                        <span>علي عبدالله</span>
                                                    </div>
                                                </td>
                                                <td><EMAIL></td>
                                                <td><span class="badge bg-info">موظف</span></td>
                                                <td><span class="badge bg-secondary">غير نشط</span></td>
                                                <td>2023-03-20</td>
                                                <td>2023-05-10 09:15</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="تعديل"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الصلاحيات -->
                        <div class="card mt-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">إعدادات الصلاحيات</h5>
                            </div>
                            <div class="card-body">
                                <form id="permissionsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>صلاحيات المدير</h6>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="adminDashboard" checked disabled>
                                                <label class="form-check-label" for="adminDashboard">
                                                    الوصول إلى لوحة التحكم
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="adminUsers" checked disabled>
                                                <label class="form-check-label" for="adminUsers">
                                                    إدارة المستخدمين والصلاحيات
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="adminSettings" checked disabled>
                                                <label class="form-check-label" for="adminSettings">
                                                    الوصول إلى الإعدادات
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="adminAllModules" checked disabled>
                                                <label class="form-check-label" for="adminAllModules">
                                                    الوصول الكامل لجميع الوحدات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>صلاحيات المحاسب</h6>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="accountantDashboard" checked>
                                                <label class="form-check-label" for="accountantDashboard">
                                                    الوصول إلى لوحة التحكم
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="accountantClients" checked>
                                                <label class="form-check-label" for="accountantClients">
                                                    إدارة العملاء
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="accountantSales" checked>
                                                <label class="form-check-label" for="accountantSales">
                                                    إدارة المبيعات
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="accountantPurchases" checked>
                                                <label class="form-check-label" for="accountantPurchases">
                                                    إدارة المشتريات
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="accountantAccounts" checked>
                                                <label class="form-check-label" for="accountantAccounts">
                                                    إدارة الحسابات
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="accountantFinance" checked>
                                                <label class="form-check-label" for="accountantFinance">
                                                    الوصول إلى المالية
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="accountantReports" checked>
                                                <label class="form-check-label" for="accountantReports">
                                                    الوصول إلى التقارير
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <h6>صلاحيات الموظف</h6>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="employeeDashboard">
                                                <label class="form-check-label" for="employeeDashboard">
                                                    الوصول إلى لوحة التحكم
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="employeeClientsView">
                                                <label class="form-check-label" for="employeeClientsView">
                                                    عرض العملاء
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="employeeClientsAdd">
                                                <label class="form-check-label" for="employeeClientsAdd">
                                                    إضافة العملاء
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="employeeReservations">
                                                <label class="form-check-label" for="employeeReservations">
                                                    إدارة الحجوزات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>صلاحيات الوكيل</h6>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="agentDashboard">
                                                <label class="form-check-label" for="agentDashboard">
                                                    الوصول إلى لوحة التحكم
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="agentClients">
                                                <label class="form-check-label" for="agentClients">
                                                    إدارة عملائه فقط
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="agentReservations">
                                                <label class="form-check-label" for="agentReservations">
                                                    إدارة حجوزات عملائه فقط
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="agentReports">
                                                <label class="form-check-label" for="agentReports">
                                                    تقارير خاصة بعملائه فقط
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 mt-3">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> حفظ الصلاحيات
                                        </button>
                                        <button type="reset" class="btn btn-secondary">
                                            <i class="fas fa-undo"></i> إعادة تعيين
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب النسخ الاحتياطي -->
                    <div class="tab-pane fade" id="backup" role="tabpanel" aria-labelledby="backup-tab">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">إعدادات النسخ الاحتياطي</h5>
                            </div>
                            <div class="card-body">
                                <form id="backupSettingsForm">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="backupFrequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                            <select class="form-select" id="backupFrequency">
                                                <option value="daily" selected>يومي</option>
                                                <option value="weekly">أسبوعي</option>
                                                <option value="monthly">شهري</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="backupTime" class="form-label">وقت النسخ الاحتياطي</label>
                                            <input type="time" class="form-control" id="backupTime" value="02:00">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="backupLocation" class="form-label">موقع حفظ النسخ الاحتياطي</label>
                                            <select class="form-select" id="backupLocation">
                                                <option value="local" selected>محلي على الخادم</option>
                                                <option value="cloud">السحابة</option>
                                                <option value="both">المحلي والسحابة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="backupRetention" class="form-label">فترة الاحتفاظ بالنسخ الاحتياطي</label>
                                            <select class="form-select" id="backupRetention">
                                                <option value="7">7 أيام</option>
                                                <option value="14">14 يوم</option>
                                                <option value="30" selected>30 يوم</option>
                                                <option value="90">90 يوم</option>
                                                <option value="365">سنة</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                                                <label class="form-check-label" for="autoBackup">
                                                    تفعيل النسخ الاحتياطي التلقائي
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="backupNotification" checked>
                                                <label class="form-check-label" for="backupNotification">
                                                    إرسال إشعار عند اكتمال النسخ الاحتياطي
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save"></i> حفظ الإعدادات
                                            </button>
                                            <button type="button" class="btn btn-success" onclick="backupNow()">
                                                <i class="fas fa-download"></i> عمل نسخة احتياطية الآن
                                            </button>
                                            <button type="reset" class="btn btn-secondary">
                                                <i class="fas fa-undo"></i> إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- النسخ الاحتياطية المتاحة -->
                        <div class="card mt-4">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">النسخ الاحتياطية المتاحة</h5>
                                <button class="btn btn-light btn-sm">
                                    <i class="fas fa-sync"></i> تحديث
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم النسخة</th>
                                                <th>تاريخ الإنشاء</th>
                                                <th>الحجم</th>
                                                <th>الموقع</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#BK001</td>
                                                <td>2023-05-15 02:00:15</td>
                                                <td>45.2 MB</td>
                                                <td>محلي</td>
                                                <td><span class="badge bg-success">مكتمل</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="استعادة"><i class="fas fa-undo"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تحميل"><i class="fas fa-download"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#BK002</td>
                                                <td>2023-05-14 02:00:23</td>
                                                <td>45.5 MB</td>
                                                <td>محلي، سحابة</td>
                                                <td><span class="badge bg-success">مكتمل</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" title="استعادة"><i class="fas fa-undo"></i></button>
                                                    <button class="btn btn-sm btn-info" title="تحميل"><i class="fas fa-download"></i></button>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#BK003</td>
                                                <td>2023-05-13 02:00:18</td>
                                                <td>45.1 MB</td>
                                                <td>محلي</td>
                                                <td><span class="badge bg-danger">فشل</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب الإشعارات -->
                    <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">إعدادات الإشعارات</h5>
                            </div>
                            <div class="card-body">
                                <form id="notificationsSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>إشعارات البريد الإلكتروني</h6>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                                <label class="form-check-label" for="emailNotifications">
                                                    تفعيل إشعارات البريد الإلكتروني
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="newClientEmail" checked>
                                                <label class="form-check-label" for="newClientEmail">
                                                    إشعار عند إضافة عميل جديد
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="newSaleEmail" checked>
                                                <label class="form-check-label" for="newSaleEmail">
                                                    إشعار عند إضافة عملية بيع جديدة
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="newPurchaseEmail" checked>
                                                <label class="form-check-label" for="newPurchaseEmail">
                                                    إشعار عند إضافة عملية شراء جديدة
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="paymentDueEmail" checked>
                                                <label class="form-check-label" for="paymentDueEmail">
                                                    إشعار عند استحقاق الدفع
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="backupEmail" checked>
                                                <label class="form-check-label" for="backupEmail">
                                                    إشعار عند اكتمال النسخ الاحتياطي
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>إشعارات داخل النظام</h6>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="systemNotifications" checked>
                                                <label class="form-check-label" for="systemNotifications">
                                                    تفعيل الإشعارات داخل النظام
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="newClientSystem" checked>
                                                <label class="form-check-label" for="newClientSystem">
                                                    إشعار عند إضافة عميل جديد
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="newSaleSystem" checked>
                                                <label class="form-check-label" for="newSaleSystem">
                                                    إشعار عند إضافة عملية بيع جديدة
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="newPurchaseSystem" checked>
                                                <label class="form-check-label" for="newPurchaseSystem">
                                                    إشعار عند إضافة عملية شراء جديدة
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="lowStockSystem" checked>
                                                <label class="form-check-label" for="lowStockSystem">
                                                    إشعار عند انخفاض المخزون
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="paymentDueSystem" checked>
                                                <label class="form-check-label" for="paymentDueSystem">
                                                    إشعار عند استحقاق الدفع
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <h6>إعدادات SMTP للبريد الإلكتروني</h6>
                                            <div class="mb-3">
                                                <label for="smtpHost" class="form-label">المضيف (Host)</label>
                                                <input type="text" class="form-control" id="smtpHost" value="smtp.example.com">
                                            </div>
                                            <div class="mb-3">
                                                <label for="smtpPort" class="form-label">المنفذ (Port)</label>
                                                <input type="number" class="form-control" id="smtpPort" value="587">
                                            </div>
                                            <div class="mb-3">
                                                <label for="smtpUsername" class="form-label">اسم المستخدم</label>
                                                <input type="text" class="form-control" id="smtpUsername" value="<EMAIL>">
                                            </div>
                                            <div class="mb-3">
                                                <label for="smtpPassword" class="form-label">كلمة المرور</label>
                                                <input type="password" class="form-control" id="smtpPassword" value="password">
                                            </div>
                                            <div class="mb-3">
                                                <label for="smtpEncryption" class="form-label">التشفير</label>
                                                <select class="form-select" id="smtpEncryption">
                                                    <option value="tls" selected>TLS</option>
                                                    <option value="ssl">SSL</option>
                                                    <option value="none">بدون تشفير</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="smtpFromEmail" class="form-label">البريد الإلكتروني المرسل</label>
                                                <input type="email" class="form-control" id="smtpFromEmail" value="<EMAIL>">
                                            </div>
                                            <div class="mb-3">
                                                <label for="smtpFromName" class="form-label">اسم المرسل</label>
                                                <input type="text" class="form-control" id="smtpFromName" value="نظام المحاسبة لوكالات السفريات">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>اختبار إعدادات البريد الإلكتروني</h6>
                                            <div class="mb-3">
                                                <label for="testEmail" class="form-label">أدخل بريدك الإلكتروني للاختبار</label>
                                                <input type="email" class="form-control" id="testEmail" placeholder="<EMAIL>">
                                            </div>
                                            <button type="button" class="btn btn-primary" onclick="testEmailSettings()">
                                                <i class="fas fa-paper-plane"></i> إرسال رسالة اختبار
                                            </button>

                                            <div class="mt-4">
                                                <h6>قوالب الإشعارات</h6>
                                                <div class="list-group">
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        قالب إشعار عميل جديد
                                                        <i class="fas fa-chevron-left"></i>
                                                    </a>
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        قالب إشعار عملية بيع جديدة
                                                        <i class="fas fa-chevron-left"></i>
                                                    </a>
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        قالب إشعار عملية شراء جديدة
                                                        <i class="fas fa-chevron-left"></i>
                                                    </a>
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        قالب إشعار استحقاق الدفع
                                                        <i class="fas fa-chevron-left"></i>
                                                    </a>
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        قالب إشعار انخفاض المخزون
                                                        <i class="fas fa-chevron-left"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 mt-3">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> حفظ الإعدادات
                                        </button>
                                        <button type="reset" class="btn btn-secondary">
                                            <i class="fas fa-undo"></i> إعادة تعيين
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نماذج الإضافة والتعديل -->
    <!-- نموذج إضافة مستخدم -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label for="userName" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="userName" required>
                        </div>
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="userEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="userPassword" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="userPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="userConfirmPassword" class="form-label">تأكيد كلمة المرور</label>
                            <input type="password" class="form-control" id="userConfirmPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="userRole" class="form-label">الدور</label>
                            <select class="form-select" id="userRole" required>
                                <option value="">اختر الدور</option>
                                <option value="admin">مدير النظام</option>
                                <option value="accountant">محاسب</option>
                                <option value="employee">موظف</option>
                                <option value="agent">وكيل</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="userPhone" class="form-label">رقم الجوال</label>
                            <input type="text" class="form-control" id="userPhone">
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="userStatus" checked>
                                <label class="form-check-label" for="userStatus">
                                    نشط
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        // دالة لحفظ المستخدم
        function saveUser() {
            const form = document.getElementById('addUserForm');
            if (form.checkValidity()) {
                const password = document.getElementById('userPassword').value;
                const confirmPassword = document.getElementById('userConfirmPassword').value;

                if (password !== confirmPassword) {
                    showErrorMessage('كلمتا المرور غير متطابقتين!');
                    return;
                }

                // هنا سيتم إضافة منطق حفظ المستخدم
                showSuccessMessage('تم حفظ المستخدم بنجاح!');

                // إغلاق النموذج
                const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
                modal.hide();

                // إعادة تعيين النموذج
                form.reset();
            } else {
                form.reportValidity();
            }
        }

        // دالة لحذف عنصر مع تأكيد
        function confirmDelete(message = 'هل أنت متأكد من أنك تريد الحذف؟') {
            return confirm(message);
        }

        // دالة لعمل نسخة احتياطية الآن
        function backupNow() {
            if (confirm('هل تريد عمل نسخة احتياطية الآن؟')) {
                showSuccessMessage('جاري عمل نسخة احتياطية...');
                // هنا سيتم إضافة منطق عمل النسخة الاحتياطية
            }
        }

        // دالة لاختبار إعدادات البريد الإلكتروني
        function testEmailSettings() {
            const email = document.getElementById('testEmail').value;
            if (!email) {
                showErrorMessage('الرجاء إدخال بريد إلكتروني صالح للاختبار');
                return;
            }

            showSuccessMessage('جاري إرسال رسالة اختبار إلى ' + email);
            // هنا سيتم إضافة منطق اختبار إعدادات البريد الإلكتروني
        }
    </script>
</body>
</html>