/* CSS للنظام المحاسبي لوكالات السفريات - تصميم حديث متطور */
:root {
    /* ألوان متدرجة رئيسية */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --info-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --danger-gradient: linear-gradient(135deg, #ff6a00 0%, #ee0979 100%);
    --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);

    /* ألوان أساسية */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #4facfe;
    --info-color: #43e97b;
    --warning-color: #fa709a;
    --danger-color: #ff6a00;
    --light-color: #f8f9fa;
    --dark-color: #212529;

    /* ألوان النيون */
    --neon-blue: #00d4ff;
    --neon-purple: #9d00ff;
    --neon-pink: #ff0080;
    --neon-green: #39ff14;

    /* متغيرات الشفافية والزجاج */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);

    /* متغيرات الظل */
    --neumorphism-shadow-light: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
    --neumorphism-shadow-dark: 8px 8px 16px #2d2d2d, -8px -8px 16px #4a4a4a;
    --neumorphism-inset-light: inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff;
    --neumorphism-inset-dark: inset 8px 8px 16px #2d2d2d, inset -8px -8px 16px #4a4a4a;
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
    color: var(--dark-color);
    min-height: 100vh;
    overflow-x: hidden;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثير الزجاج (Glassmorphism) */
.glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* تأثير النيون (Neon) */
.neon-text {
    color: var(--neon-blue);
    text-shadow: 0 0 5px var(--neon-blue), 0 0 10px var(--neon-blue), 0 0 15px var(--neon-blue), 0 0 20px var(--neon-blue);
}

.neon-border {
    box-shadow: 0 0 5px var(--neon-blue), 0 0 10px var(--neon-blue), 0 0 15px var(--neon-blue), 0 0 20px var(--neon-blue);
    border: 1px solid var(--neon-blue);
}

/* تأثير النيومورفيزم (Neumorphism) */
.neumorphism-light {
    background: #e0e5ec;
    border-radius: 20px;
    box-shadow: var(--neumorphism-shadow-light);
}

.neumorphism-dark {
    background: #2d2d2d;
    border-radius: 20px;
    box-shadow: var(--neumorphism-shadow-dark);
}

.neumorphism-inset-light {
    background: #e0e5ec;
    border-radius: 20px;
    box-shadow: var(--neumorphism-inset-light);
}

.neumorphism-inset-dark {
    background: #2d2d2d;
    border-radius: 20px;
    box-shadow: var(--neumorphism-inset-dark);
}

/* شريط التنقل */
.navbar {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 15px rgba(102, 126, 234, 0.7);
}

/* الشريط الجانبي */
.sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-left: 1px solid var(--glass-border);
    min-height: calc(100vh - 56px);
    padding: 20px 0;
    box-shadow: 8px 0 32px 0 rgba(31, 38, 135, 0.37);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 25px;
    margin: 8px 15px;
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
    z-index: -1;
    border-radius: 15px;
}

.sidebar .nav-link:hover::before {
    width: 100%;
}

.sidebar .nav-link:hover {
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: white;
    background: var(--primary-gradient);
    box-shadow: 0 0 15px rgba(102, 126, 234, 0.7);
}

.sidebar .nav-link i {
    margin-left: 10px;
    font-size: 1.1rem;
}

/* المحتوى الرئيسي */
.main-content {
    padding: 25px;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 20px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px 0 rgba(31, 38, 135, 0.5);
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 20px 20px 0 0 !important;
    padding: 15px 20px;
    font-weight: 600;
}

.card-header h5 {
    margin: 0;
    font-size: 1.2rem;
}

.card-body {
    padding: 20px;
}

/* بطاقات لوحة التحكم */
.dashboard-card {
    border-radius: 20px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.dashboard-card:hover::before {
    opacity: 0.1;
}

.dashboard-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px 0 rgba(31, 38, 135, 0.5);
}

.dashboard-card .card-body {
    padding: 25px 20px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.dashboard-card i {
    font-size: 3rem;
    margin-bottom: 15px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.dashboard-card h5 {
    margin-bottom: 10px;
    font-weight: 600;
    color: white;
}

.dashboard-card h3 {
    margin-bottom: 15px;
    font-weight: 700;
    color: white;
}

.dashboard-card .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dashboard-card .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: scale(1.05);
}

/* الأزرار */
.btn {
    border-radius: 50px;
    font-weight: 500;
    padding: 10px 25px;
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: width 0.3s ease;
    z-index: -1;
    border-radius: 50px;
}

.btn:hover::before {
    width: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-info {
    background: var(--info-gradient);
    color: white;
}

.btn-warning {
    background: var(--warning-gradient);
    color: white;
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
}

/* النماذج */
.form-control, .form-select {
    border-radius: 15px;
    border: 1px solid var(--glass-border);
    background: var(--glass-bg);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    color: white;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--neon-blue);
    box-shadow: 0 0 0 0.25rem rgba(0, 212, 255, 0.25);
    color: white;
}

.form-label {
    color: white;
    font-weight: 500;
    margin-bottom: 8px;
}

/* الجداول */
.table {
    background: var(--glass-bg);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-radius: 15px;
    overflow: hidden;
    color: white;
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    font-weight: 600;
    border: none;
    padding: 15px;
}

.table tbody td {
    border-color: rgba(255, 255, 255, 0.1);
    padding: 12px 15px;
    vertical-align: middle;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* شارات الحالة */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
}

.status-processing {
    background: rgba(255, 243, 205, 0.3);
    color: #fff3cd;
    border: 1px solid #fff3cd;
}

.status-execution-office {
    background: rgba(207, 226, 255, 0.3);
    color: #cfe2ff;
    border: 1px solid #cfe2ff;
}

.status-execution-embassy {
    background: rgba(226, 217, 243, 0.3);
    color: #e2d9f3;
    border: 1px solid #e2d9f3;
}

.status-indicator-embassy {
    background: rgba(248, 215, 218, 0.3);
    color: #f8d7da;
    border: 1px solid #f8d7da;
}

.status-indicator-office {
    background: rgba(209, 231, 221, 0.3);
    color: #d1e7dd;
    border: 1px solid #d1e7dd;
}

.status-delivered-indicator {
    background: rgba(207, 244, 252, 0.3);
    color: #cff4fc;
    border: 1px solid #cff4fc;
}

.status-returned {
    background: rgba(248, 215, 218, 0.3);
    color: #f8d7da;
    border: 1px solid #f8d7da;
}

.status-cancelled {
    background: rgba(226, 227, 229, 0.3);
    color: #e2e3e5;
    border: 1px solid #e2e3e5;
}

.status-withdrawn {
    background: rgba(248, 215, 218, 0.3);
    color: #f8d7da;
    border: 1px solid #f8d7da;
}

.status-note {
    background: rgba(255, 243, 205, 0.3);
    color: #fff3cd;
    border: 1px solid #fff3cd;
}

/* التبويبات */
.nav-tabs {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-tabs .nav-link {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    border: none;
    border-radius: 15px 15px 0 0;
    padding: 12px 20px;
    margin-right: 5px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.nav-tabs .nav-link.active {
    color: white;
    background: var(--primary-gradient);
    border: none;
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: 20px;
    border: none;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: var(--primary-gradient);
    color: white;
    border-radius: 20px 20px 0 0;
}

.modal-title {
    color: white;
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* تأثيرات الحركة */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
    100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* تأثيرات التحميل */
.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--neon-blue);
    animation: spin 1s ease-in-out infinite;
    margin: 20px auto;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تأثيرات النصوص */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
}

/* الاستجابة للشاشات المختلفة */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
        padding: 10px 0;
        border-left: none;
        border-top: 1px solid var(--glass-border);
    }

    .main-content {
        padding: 15px;
    }

    .dashboard-card {
        margin-bottom: 20px;
    }

    .card {
        margin-bottom: 20px;
    }

    .table-responsive {
        border-radius: 15px;
        overflow: hidden;
    }
}

/* تأثيرات ثلاثية الأبعاد للرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    perspective: 1000px;
}

.chart-3d {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.5s;
}

.chart-3d:hover {
    transform: rotateX(10deg) rotateY(10deg);
}

/* تأثيرات النيون للعناصر التفاعلية */
.interactive-element {
    position: relative;
    transition: all 0.3s ease;
}

.interactive-element::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
    box-shadow: 0 0 15px var(--neon-blue), 0 0 30px var(--neon-blue);
}

.interactive-element:hover::after {
    opacity: 1;
}

/* تأثيرات التمرير */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}