<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المشتريات - نظام المحاسبة لوكالات السفريات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">نظام المحاسبة لوكالات السفريات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> المدير
                        </a>
                        <ul class="dropdown-menu dropdown-menu-start">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-2 sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reservations.html">
                            <i class="fas fa-calendar-check"></i> الحجوزات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="agents.html">
                            <i class="fas fa-user-tie"></i> الوكلاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.html">
                            <i class="fas fa-truck"></i> الموردون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.html">
                            <i class="fas fa-warehouse"></i> المخزون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sales.html">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="purchases.html">
                            <i class="fas fa-hand-holding-usd"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="accounts.html">
                            <i class="fas fa-file-invoice-dollar"></i> الحسابات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="finance.html">
                            <i class="fas fa-chart-line"></i> المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="templates.html">
                            <i class="fas fa-file-contract"></i> القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.html">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>المشتريات</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPurchaseModal">
                        <i class="fas fa-plus"></i> إضافة عملية شراء جديدة
                    </button>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-primary text-white">
                            <div class="card-body">
                                <i class="fas fa-hand-holding-usd"></i>
                                <h5>إجمالي المشتريات</h5>
                                <h3>187</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-success text-white">
                            <div class="card-body">
                                <i class="fas fa-money-bill-wave"></i>
                                <h5>إجمالي المصروفات</h5>
                                <h3>3,450,000 ريال</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-info text-white">
                            <div class="card-body">
                                <i class="fas fa-percentage"></i>
                                <h5>متوسط الشراء</h5>
                                <h3>18,451 ريال</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-warning text-white">
                            <div class="card-body">
                                <i class="fas fa-exclamation-triangle"></i>
                                <h5>المدفوعات المستحقة</h5>
                                <h3>560,000 ريال</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلترة البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form id="searchForm">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="searchInvoiceNumber" class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" id="searchInvoiceNumber" placeholder="ابحث برقم الفاتورة">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchSupplierName" class="form-label">اسم المورد</label>
                                    <input type="text" class="form-control" id="searchSupplierName" placeholder="ابحث باسم المورد">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchDateFrom" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="searchDateFrom">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchDateTo" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="searchDateTo">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchPaymentMethod" class="form-label">طريقة الدفع</label>
                                    <select class="form-select" id="searchPaymentMethod">
                                        <option value="">الكل</option>
                                        <option value="cash">نقداً</option>
                                        <option value="bank">تحويل بنكي</option>
                                        <option value="card">بطاقة ائتمان</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="searchStatus" class="form-label">الحالة</label>
                                    <select class="form-select" id="searchStatus">
                                        <option value="">الكل</option>
                                        <option value="paid">مدفوعة</option>
                                        <option value="partial">مدفوعة جزئياً</option>
                                        <option value="unpaid">غير مدفوعة</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-redo"></i> إعادة تعيين
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول المشتريات -->
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">قائمة المشتريات</h5>
                        <div>
                            <button class="btn btn-sm btn-light">
                                <i class="fas fa-file-excel"></i> تصدير إكسل
                            </button>
                            <button class="btn btn-sm btn-light">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>تاريخ الفاتورة</th>
                                        <th>اسم المورد</th>
                                        <th>الوصف</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                        <th>طريقة الدفع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>#7001</td>
                                        <td>2023-05-15</td>
                                        <td>شركة النقل السريع</td>
                                        <td>تذاكر باصات</td>
                                        <td>10</td>
                                        <td>15,000 ريال</td>
                                        <td>150,000 ريال</td>
                                        <td>تحويل بنكي</td>
                                        <td><span class="badge bg-success">مدفوعة</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#7002</td>
                                        <td>2023-05-14</td>
                                        <td>فنادق الراحة</td>
                                        <td>حجوزات فنادق</td>
                                        <td>5</td>
                                        <td>50,000 ريال</td>
                                        <td>250,000 ريال</td>
                                        <td>نقداً</td>
                                        <td><span class="badge bg-warning">مدفوعة جزئياً</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#7003</td>
                                        <td>2023-05-13</td>
                                        <td>مكتبة المعارف</td>
                                        <td>نماذج طلبات</td>
                                        <td>100</td>
                                        <td>1,000 ريال</td>
                                        <td>100,000 ريال</td>
                                        <td>بطاقة ائتمان</td>
                                        <td><span class="badge bg-danger">غير مدفوعة</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة عملية شراء جديدة -->
    <div class="modal fade" id="addPurchaseModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة عملية شراء جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form id="addPurchaseForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="purchaseDate" class="form-label">تاريخ الشراء</label>
                                <input type="date" class="form-control" id="purchaseDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="supplierName" class="form-label">اسم المورد</label>
                                <select class="form-select" id="supplierName" required>
                                    <option value="">اختر المورد</option>
                                    <option value="1">شركة النقل السريع</option>
                                    <option value="2">فنادق الراحة</option>
                                    <option value="3">مكتبة المعارف</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="itemDescription" class="form-label">وصف الصنف</label>
                                <input type="text" class="form-control" id="itemDescription" placeholder="أدخل وصف الصنف" required>
                            </div>
                            <div class="col-md-6">
                                <label for="itemCategory" class="form-label">فئة الصنف</label>
                                <select class="form-select" id="itemCategory" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="transport">النقل</option>
                                    <option value="accommodation">الإقامة</option>
                                    <option value="food">الطعام</option>
                                    <option value="services">الخدمات</option>
                                    <option value="equipment">المعدات</option>
                                    <option value="documents">الوثائق</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="quantity" class="form-label">الكمية</label>
                                <input type="number" class="form-control" id="quantity" min="1" required>
                            </div>
                            <div class="col-md-4">
                                <label for="unitPrice" class="form-label">سعر الوحدة</label>
                                <input type="number" class="form-control" id="unitPrice" min="0" step="0.01" required>
                            </div>
                            <div class="col-md-4">
                                <label for="totalPrice" class="form-label">الإجمالي</label>
                                <input type="number" class="form-control" id="totalPrice" min="0" step="0.01" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="paymentMethod" class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="paymentMethod" required>
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash">نقداً</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="card">بطاقة ائتمان</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="paymentStatus" class="form-label">حالة الدفع</label>
                                <select class="form-select" id="paymentStatus" required>
                                    <option value="">اختر الحالة</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="partial">مدفوعة جزئياً</option>
                                    <option value="unpaid">غير مدفوعة</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="paidAmount" class="form-label">المبلغ المدفوع</label>
                                <input type="number" class="form-control" id="paidAmount" min="0" step="0.01">
                            </div>
                            <div class="col-md-6">
                                <label for="dueDate" class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" id="dueDate">
                            </div>
                            <div class="col-12">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="savePurchase()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        // حساب الإجمالي تلقائياً عند تغيير الكمية أو السعر
        document.addEventListener('DOMContentLoaded', function() {
            const quantityInput = document.getElementById('quantity');
            const unitPriceInput = document.getElementById('unitPrice');
            const totalPriceInput = document.getElementById('totalPrice');

            function calculateTotal() {
                const quantity = parseFloat(quantityInput.value) || 0;
                const unitPrice = parseFloat(unitPriceInput.value) || 0;
                totalPriceInput.value = (quantity * unitPrice).toFixed(2);
            }

            quantityInput.addEventListener('input', calculateTotal);
            unitPriceInput.addEventListener('input', calculateTotal);
        });

        // حفظ عملية الشراء
        function savePurchase() {
            // هنا سيتم إضافة منطق حفظ عملية الشراء
            showSuccessMessage('تم حفظ عملية الشراء بنجاح!');

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addPurchaseModal'));
            modal.hide();

            // إعادة تعيين النموذج
            document.getElementById('addPurchaseForm').reset();
        }
    </script>
</body>
</html>