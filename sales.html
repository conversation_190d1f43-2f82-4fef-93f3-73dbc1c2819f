<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المبيعات - نظام المحاسبة لوكالات السفريات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">نظام المحاسبة لوكالات السفريات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> المدير
                        </a>
                        <ul class="dropdown-menu dropdown-menu-start">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-2 sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reservations.html">
                            <i class="fas fa-calendar-check"></i> الحجوزات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="agents.html">
                            <i class="fas fa-user-tie"></i> الوكلاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.html">
                            <i class="fas fa-truck"></i> الموردون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.html">
                            <i class="fas fa-warehouse"></i> المخزون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="sales.html">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="purchases.html">
                            <i class="fas fa-hand-holding-usd"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="accounts.html">
                            <i class="fas fa-file-invoice-dollar"></i> الحسابات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="finance.html">
                            <i class="fas fa-chart-line"></i> المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="templates.html">
                            <i class="fas fa-file-contract"></i> القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.html">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>المبيعات</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSaleModal">
                        <i class="fas fa-plus"></i> إضافة عملية بيع جديدة
                    </button>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-primary text-white">
                            <div class="card-body">
                                <i class="fas fa-shopping-cart"></i>
                                <h5>إجمالي المبيعات</h5>
                                <h3>245</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-success text-white">
                            <div class="card-body">
                                <i class="fas fa-money-bill-wave"></i>
                                <h5>إجمالي الإيرادات</h5>
                                <h3>5,250,000 ريال</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-info text-white">
                            <div class="card-body">
                                <i class="fas fa-percentage"></i>
                                <h5>متوسط البيع</h5>
                                <h3>21,428 ريال</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-warning text-white">
                            <div class="card-body">
                                <i class="fas fa-chart-line"></i>
                                <h5>نمو المبيعات</h5>
                                <h3>12%</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلترة البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form id="searchForm">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="searchInvoiceNumber" class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" id="searchInvoiceNumber" placeholder="ابحث برقم الفاتورة">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchClientName" class="form-label">اسم العميل</label>
                                    <input type="text" class="form-control" id="searchClientName" placeholder="ابحث باسم العميل">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchDateFrom" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="searchDateFrom">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchDateTo" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="searchDateTo">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchPaymentMethod" class="form-label">طريقة الدفع</label>
                                    <select class="form-select" id="searchPaymentMethod">
                                        <option value="">الكل</option>
                                        <option value="cash">نقداً</option>
                                        <option value="bank">تحويل بنكي</option>
                                        <option value="card">بطاقة ائتمان</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="searchStatus" class="form-label">الحالة</label>
                                    <select class="form-select" id="searchStatus">
                                        <option value="">الكل</option>
                                        <option value="paid">مدفوعة</option>
                                        <option value="partial">مدفوعة جزئياً</option>
                                        <option value="unpaid">غير مدفوعة</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-redo"></i> إعادة تعيين
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول المبيعات -->
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">قائمة المبيعات</h5>
                        <div>
                            <button class="btn btn-sm btn-light">
                                <i class="fas fa-file-excel"></i> تصدير إكسل
                            </button>
                            <button class="btn btn-sm btn-light">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>اسم العميل</th>
                                        <th>الخدمة</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>المبلغ المدفوع</th>
                                        <th>طريقة الدفع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>#S1001</td>
                                        <td>2023-05-15</td>
                                        <td>أحمد محمد</td>
                                        <td>تأشيرة عمل فردي</td>
                                        <td>50,000 ريال</td>
                                        <td>50,000 ريال</td>
                                        <td>نقداً</td>
                                        <td><span class="badge bg-success">مدفوعة</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                            <button class="btn btn-sm btn-secondary" title="طباعة"><i class="fas fa-print"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#S1002</td>
                                        <td>2023-05-14</td>
                                        <td>علي عبدالله</td>
                                        <td>حزمة عمرة عادية</td>
                                        <td>150,000 ريال</td>
                                        <td>75,000 ريال</td>
                                        <td>تحويل بنكي</td>
                                        <td><span class="badge bg-warning">مدفوعة جزئياً</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                            <button class="btn btn-sm btn-secondary" title="طباعة"><i class="fas fa-print"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#S1003</td>
                                        <td>2023-05-13</td>
                                        <td>خالد سالم</td>
                                        <td>تأشيرة زيارة عائلية</td>
                                        <td>30,000 ريال</td>
                                        <td>0 ريال</td>
                                        <td>بطاقة ائتمان</td>
                                        <td><span class="badge bg-danger">غير مدفوعة</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" title="عرض"><i class="fas fa-eye"></i></button>
                                            <button class="btn btn-sm btn-info" title="تعديل"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="return confirmDelete()"><i class="fas fa-trash"></i></button>
                                            <button class="btn btn-sm btn-secondary" title="طباعة"><i class="fas fa-print"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- ترقيم الصفحات -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">السابق</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">التالي</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة عملية بيع جديدة -->
    <div class="modal fade" id="addSaleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">إضافة عملية بيع جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addSaleForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="saleClient" class="form-label">العميل</label>
                                <select class="form-select" id="saleClient" required>
                                    <option value="">اختر العميل</option>
                                    <option value="1">أحمد محمد</option>
                                    <option value="2">علي عبدالله</option>
                                    <option value="3">خالد سالم</option>
                                    <option value="new">+ إضافة عميل جديد</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="saleDate" class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="saleDate" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الخدمات</label>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="servicesTable">
                                    <thead>
                                        <tr>
                                            <th>الخدمة</th>
                                            <th>الوصف</th>
                                            <th>السعر</th>
                                            <th>الكمية</th>
                                            <th>المجموع</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <select class="form-select service-select">
                                                    <option value="">اختر الخدمة</option>
                                                    <option value="visa-individual">تأشيرة عمل فردي</option>
                                                    <option value="visa-normal">تأشيرة عمل عادي</option>
                                                    <option value="visa-family">تأشيرة زيارة عائلية</option>
                                                    <option value="umrah-economy">حزمة عمرة اقتصادية</option>
                                                    <option value="umrah-standard">حزمة عمرة عادية</option>
                                                    <option value="umrah-premium">حزمة عمرة مميزة</option>
                                                </select>
                                            </td>
                                            <td><input type="text" class="form-control service-description" placeholder="الوصف"></td>
                                            <td><input type="number" class="form-control service-price" placeholder="السعر"></td>
                                            <td><input type="number" class="form-control service-quantity" value="1" min="1"></td>
                                            <td><input type="number" class="form-control service-total" readonly></td>
                                            <td><button type="button" class="btn btn-danger btn-sm remove-service"><i class="fas fa-trash"></i></button></td>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="6" class="text-end">
                                                <button type="button" class="btn btn-sm btn-success" id="addServiceRow">
                                                    <i class="fas fa-plus"></i> إضافة خدمة
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="4" class="text-end fw-bold">الإجمالي:</td>
                                            <td colspan="2"><input type="number" class="form-control fw-bold" id="grandTotal" readonly></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="paymentMethod" class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="paymentMethod" required>
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash">نقداً</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="card">بطاقة ائتمان</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="paidAmount" class="form-label">المبلغ المدفوع</label>
                                <input type="number" class="form-control" id="paidAmount" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="saleNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="saleNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveSale()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        // حساب المجموع تلقائياً عند تغيير السعر أو الكمية
        document.addEventListener('DOMContentLoaded', function() {
            const servicesTable = document.getElementById('servicesTable');

            // إضافة صف خدمة جديد
            document.getElementById('addServiceRow').addEventListener('click', function() {
                const tbody = servicesTable.querySelector('tbody');
                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td>
                        <select class="form-select service-select">
                            <option value="">اختر الخدمة</option>
                            <option value="visa-individual">تأشيرة عمل فردي</option>
                            <option value="visa-normal">تأشيرة عمل عادي</option>
                            <option value="visa-family">تأشيرة زيارة عائلية</option>
                            <option value="umrah-economy">حزمة عمرة اقتصادية</option>
                            <option value="umrah-standard">حزمة عمرة عادية</option>
                            <option value="umrah-premium">حزمة عمرة مميزة</option>
                        </select>
                    </td>
                    <td><input type="text" class="form-control service-description" placeholder="الوصف"></td>
                    <td><input type="number" class="form-control service-price" placeholder="السعر"></td>
                    <td><input type="number" class="form-control service-quantity" value="1" min="1"></td>
                    <td><input type="number" class="form-control service-total" readonly></td>
                    <td><button type="button" class="btn btn-danger btn-sm remove-service"><i class="fas fa-trash"></i></button></td>
                `;
                tbody.appendChild(newRow);
                attachServiceListeners(newRow);
            });

            // إضافة مستمعي الأحداث لصف الخدمة الأولي
            const initialRows = servicesTable.querySelectorAll('tbody tr');
            initialRows.forEach(row => {
                attachServiceListeners(row);
            });

            // حساب الإجمالي الكلي
            updateGrandTotal();
        });

        // إضافة مستمعي الأحداث لصف الخدمة
        function attachServiceListeners(row) {
            const priceInput = row.querySelector('.service-price');
            const quantityInput = row.querySelector('.service-quantity');
            const totalInput = row.querySelector('.service-total');
            const removeButton = row.querySelector('.remove-service');

            // حساب المجموع عند تغيير السعر أو الكمية
            function calculateTotal() {
                const price = parseFloat(priceInput.value) || 0;
                const quantity = parseFloat(quantityInput.value) || 0;
                const total = price * quantity;
                totalInput.value = total.toFixed(2);
                updateGrandTotal();
            }

            priceInput.addEventListener('input', calculateTotal);
            quantityInput.addEventListener('input', calculateTotal);

            // حذف صف الخدمة
            removeButton.addEventListener('click', function() {
                row.remove();
                updateGrandTotal();
            });
        }

        // تحديث الإجمالي الكلي
        function updateGrandTotal() {
            const servicesTable = document.getElementById('servicesTable');
            const totalInputs = servicesTable.querySelectorAll('.service-total');
            let grandTotal = 0;

            totalInputs.forEach(input => {
                grandTotal += parseFloat(input.value) || 0;
            });

            document.getElementById('grandTotal').value = grandTotal.toFixed(2);
        }

        // حفظ عملية البيع
        function saveSale() {
            const form = document.getElementById('addSaleForm');

            if (form.checkValidity()) {
                // هنا سيتم إضافة منطق حفظ عملية البيع
                showSuccessMessage('تم حفظ عملية البيع بنجاح');

                // إغلاق النافذة
                const modal = bootstrap.Modal.getInstance(document.getElementById('addSaleModal'));
                modal.hide();

                // إعادة تعيين النموذج
                form.reset();

                // إعادة تعيين جدول الخدمات
                const tbody = document.querySelector('#servicesTable tbody');
                tbody.innerHTML = `
                    <tr>
                        <td>
                            <select class="form-select service-select">
                                <option value="">اختر الخدمة</option>
                                <option value="visa-individual">تأشيرة عمل فردي</option>
                                <option value="visa-normal">تأشيرة عمل عادي</option>
                                <option value="visa-family">تأشيرة زيارة عائلية</option>
                                <option value="umrah-economy">حزمة عمرة اقتصادية</option>
                                <option value="umrah-standard">حزمة عمرة عادية</option>
                                <option value="umrah-premium">حزمة عمرة مميزة</option>
                            </select>
                        </td>
                        <td><input type="text" class="form-control service-description" placeholder="الوصف"></td>
                        <td><input type="number" class="form-control service-price" placeholder="السعر"></td>
                        <td><input type="number" class="form-control service-quantity" value="1" min="1"></td>
                        <td><input type="number" class="form-control service-total" readonly></td>
                        <td><button type="button" class="btn btn-danger btn-sm remove-service"><i class="fas fa-trash"></i></button></td>
                    </tr>
                `;

                // إضافة مستمعي الأحداث لصف الخدمة الأولي
                attachServiceListeners(tbody.querySelector('tr'));

                // حساب الإجمالي الكلي
                updateGrandTotal();
            } else {
                showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
            }
        }
    </script>
</body>
</html>