// JavaScript للنظام المحاسبي لوكالات السفريات - النسخة المحسنة

// استيراد الوحدات
import { initAllEffects } from './modules/effects.js';
import {
  createLineChart,
  createPieChart,
  createDoughnutChart,
  createBarChart,
  createMonthlySalesData,
  createBookingDistributionData
} from './modules/charts.js';

// تنفيذ الكود عند اكتمال تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  // تهيئة جميع التأثيرات البصرية
  initAllEffects();
  
  // تهيئة الرسوم البيانية إذا كانت موجودة في الصفحة
  initCharts();
  
  // معالجة نموذج تسجيل الدخول
  initLoginForm();
  
  // معالجة نموذج إنشاء حساب جديد
  initRegisterForm();
  
  // تهيئة الشريط الجانبي المتجاوب
  initResponsiveSidebar();
  
  // تهيئة النوافذ المنبثقة
  initModals();
  
  // تهيئة التبويبات
  initTabs();
  
  // تهيئة التلميحات
  initTooltips();
  
  // تهيئة الجداول القابلة للفرز
  initSortableTables();
});

// تهيئة الرسوم البيانية
function initCharts() {
  // رسم بياني للمبيعات الشهرية
  const salesChartCanvas = document.getElementById('salesChart');
  if (salesChartCanvas) {
    createLineChart('salesChart', createMonthlySalesData());
  }
  
  // رسم بياني لتوزيع الحجوزات
  const bookingDistributionCanvas = document.getElementById('bookingDistributionChart');
  if (bookingDistributionCanvas) {
    createDoughnutChart('bookingDistributionChart', createBookingDistributionData(), {
      plugins: {
        legend: {
          position: 'right'
        }
      }
    });
  }
  
  // رسم بياني للإيرادات حسب نوع الخدمة
  const revenueByServiceCanvas = document.getElementById('revenueByServiceChart');
  if (revenueByServiceCanvas) {
    createBarChart('revenueByServiceChart', {
      labels: ['تذاكر طيران', 'حجوزات فنادق', 'تأشيرات', 'رحلات سياحية', 'خدمات أخرى'],
      datasets: [
        {
          label: 'الإيرادات (بالريال)',
          data: [125000, 85000, 65000, 45000, 25000],
          backgroundColor: [
            'rgba(102, 126, 234, 0.7)',
            'rgba(118, 75, 162, 0.7)',
            'rgba(79, 172, 254, 0.7)',
            'rgba(250, 112, 154, 0.7)',
            'rgba(67, 233, 123, 0.7)'
          ],
          borderColor: [
            'rgba(102, 126, 234, 1)',
            'rgba(118, 75, 162, 1)',
            'rgba(79, 172, 254, 1)',
            'rgba(250, 112, 154, 1)',
            'rgba(67, 233, 123, 1)'
          ],
          borderWidth: 1
        }
      ]
    });
  }
}

// تهيئة نموذج تسجيل الدخول
function initLoginForm() {
  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    loginForm.addEventListener('submit', function(e) {
      e.preventDefault();
      const email = document.getElementById('loginEmail').value;
      const password = document.getElementById('loginPassword').value;
      
      // التحقق من صحة البيانات
      if (!email || !password) {
        showErrorMessage('يرجى إدخال البريد الإلكتروني وكلمة المرور');
        return;
      }
      
      // هنا سيتم إضافة منطق التحقق من تسجيل الدخول
      console.log('محاولة تسجيل الدخول:', { email, password });
      
      // إظهار مؤشر التحميل
      showLoading();
      
      // محاكاة تأخير الاتصال بالخادم
      setTimeout(() => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // بعد التحقق بنجاح، سيتم توجيه المستخدم إلى الصفحة الرئيسية
        window.location.href = 'dashboard.html';
      }, 1500);
    });
  }
}

// تهيئة نموذج إنشاء حساب جديد
function initRegisterForm() {
  const registerForm = document.getElementById('registerForm');
  if (registerForm) {
    registerForm.addEventListener('submit', function(e) {
      e.preventDefault();
      const name = document.getElementById('registerName').value;
      const email = document.getElementById('registerEmail').value;
      const password = document.getElementById('registerPassword').value;
      const confirmPassword = document.getElementById('registerConfirmPassword').value;
      const agency = document.getElementById('registerAgency').value;
      
      // التحقق من صحة البيانات
      if (!name || !email || !password || !confirmPassword || !agency) {
        showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
        return;
      }
      
      // التحقق من تطابق كلمتي المرور
      if (password !== confirmPassword) {
        showErrorMessage('كلمتا المرور غير متطابقتين!');
        return;
      }
      
      // التحقق من صحة البريد الإلكتروني
      if (!isValidEmail(email)) {
        showErrorMessage('يرجى إدخال بريد إلكتروني صحيح');
        return;
      }
      
      // هنا سيتم إضافة منطق إنشاء حساب جديد
      console.log('محاولة إنشاء حساب جديد:', { name, email, password, agency });
      
      // إظهار مؤشر التحميل
      showLoading();
      
      // محاكاة تأخير الاتصال بالخادم
      setTimeout(() => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // بعد إنشاء الحساب بنجاح
        showSuccessMessage('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.');
        
        // التبديل إلى تبويب تسجيل الدخول
        const loginTab = document.getElementById('login-tab');
        if (loginTab) {
          const tabInstance = new bootstrap.Tab(loginTab);
          tabInstance.show();
        }
        
        // إعادة تعيين النموذج
        registerForm.reset();
      }, 1500);
    });
  }
}

// تهيئة الشريط الجانبي المتجاوب
function initResponsiveSidebar() {
  const sidebarToggle = document.getElementById('sidebarToggle');
  const sidebar = document.querySelector('.sidebar');
  
  if (sidebarToggle && sidebar) {
    sidebarToggle.addEventListener('click', function() {
      sidebar.classList.toggle('show');
    });
    
    // إغلاق الشريط الجانبي عند النقر خارجه في الشاشات الصغيرة
    document.addEventListener('click', function(e) {
      if (window.innerWidth < 992 && 
          sidebar.classList.contains('show') && 
          !sidebar.contains(e.target) && 
          e.target !== sidebarToggle) {
        sidebar.classList.remove('show');
      }
    });
  }
}

// تهيئة النوافذ المنبثقة
function initModals() {
  // إضافة تأثيرات للنوافذ المنبثقة
  const modals = document.querySelectorAll('.modal');
  
  modals.forEach(modal => {
    modal.addEventListener('show.bs.modal', function() {
      // إضافة تأثير ظهور متدرج
      this.style.opacity = '0';
      setTimeout(() => {
        this.style.transition = 'opacity 0.3s ease';
        this.style.opacity = '1';
      }, 50);
    });
    
    modal.addEventListener('hide.bs.modal', function() {
      // إضافة تأثير اختفاء متدرج
      this.style.opacity = '0';
    });
  });
}

// تهيئة التبويبات
function initTabs() {
  const tabLinks = document.querySelectorAll('.nav-tabs .nav-link');
  
  tabLinks.forEach(link => {
    link.addEventListener('click', function() {
      // إضافة تأثير النقر
      this.classList.add('pulse-animation');
      setTimeout(() => {
        this.classList.remove('pulse-animation');
      }, 1000);
    });
  });
}

// تهيئة التلميحات
function initTooltips() {
  // تهيئة جميع عناصر التلميح
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function(tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl, {
      template: '<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner glass"></div></div>'
    });
  });
}

// تهيئة الجداول القابلة للفرز
function initSortableTables() {
  const tables = document.querySelectorAll('.table-sortable');
  
  tables.forEach(table => {
    const headers = table.querySelectorAll('th[data-sort]');
    
    headers.forEach(header => {
      header.style.cursor = 'pointer';
      header.innerHTML += ' <i class="fas fa-sort"></i>';
      
      header.addEventListener('click', function() {
        const sortKey = this.getAttribute('data-sort');
        const sortDirection = this.getAttribute('data-direction') || 'asc';
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        // تغيير اتجاه الفرز
        const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        this.setAttribute('data-direction', newDirection);
        
        // تحديث أيقونة الفرز
        headers.forEach(h => {
          h.querySelector('i').className = 'fas fa-sort';
        });
        this.querySelector('i').className = newDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        
        // فرز الصفوف
        rows.sort((a, b) => {
          const aValue = a.querySelector(`td[data-${sortKey}]`).getAttribute(`data-${sortKey}`) || 
                       a.querySelector(`td:nth-child(${Array.from(a.parentNode.children).indexOf(a) + 1})`).textContent.trim();
          const bValue = b.querySelector(`td[data-${sortKey}]`).getAttribute(`data-${sortKey}`) || 
                       b.querySelector(`td:nth-child(${Array.from(b.parentNode.children).indexOf(b) + 1})`).textContent.trim();
          
          // التحقق مما إذا كانت القيم أرقام
          const aNum = parseFloat(aValue);
          const bNum = parseFloat(bValue);
          
          if (!isNaN(aNum) && !isNaN(bNum)) {
            return newDirection === 'asc' ? aNum - bNum : bNum - aNum;
          }
          
          // فرز نصي
          return newDirection === 'asc' ? 
            aValue.localeCompare(bValue, 'ar') : 
            bValue.localeCompare(aValue, 'ar');
        });
        
        // إعادة ترتيب الصفوف
        rows.forEach(row => {
          tbody.appendChild(row);
        });
        
        // إضافة تأثير للصفوف المفروزة
        rows.forEach((row, index) => {
          row.style.transition = `transform 0.5s ease ${index * 0.05}s, opacity 0.5s ease ${index * 0.05}s`;
          row.style.opacity = '0';
          row.style.transform = 'translateY(20px)';
          
          setTimeout(() => {
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
          }, 50);
        });
      });
    });
  });
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// دالة لتأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من أنك تريد الحذف؟') {
  return Swal.fire({
    title: 'تأكيد الحذف',
    text: message,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#ff6a00',
    cancelButtonColor: '#6c757d',
    confirmButtonText: 'نعم، احذف',
    cancelButtonText: 'إلغاء',
    background: 'rgba(40, 40, 40, 0.8)',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    customClass: {
      popup: 'glass',
      title: 'text-white',
      htmlContainer: 'text-white'
    }
  }).then((result) => {
    return result.isConfirmed;
  });
}

// دالة لعرض رسائل النجاح
function showSuccessMessage(message) {
  Swal.fire({
    title: 'تم بنجاح',
    text: message,
    icon: 'success',
    timer: 3000,
    timerProgressBar: true,
    showConfirmButton: false,
    background: 'rgba(40, 40, 40, 0.8)',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    customClass: {
      popup: 'glass',
      title: 'text-white',
      htmlContainer: 'text-white'
    }
  });
}

// دالة لعرض رسائل الخطأ
function showErrorMessage(message) {
  Swal.fire({
    title: 'خطأ',
    text: message,
    icon: 'error',
    confirmButtonColor: '#667eea',
    background: 'rgba(40, 40, 40, 0.8)',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    customClass: {
      popup: 'glass',
      title: 'text-white',
      htmlContainer: 'text-white'
    }
  });
}

// دالة لعرض مؤشر التحميل
function showLoading() {
  Swal.fire({
    title: 'جاري التحميل...',
    html: '<div class="spinner-border text-light" role="status"><span class="visually-hidden">جاري التحميل...</span></div>',
    showConfirmButton: false,
    allowOutsideClick: false,
    background: 'rgba(40, 40, 40, 0.8)',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    customClass: {
      popup: 'glass',
      title: 'text-white'
    }
  });
}

// دالة لإخفاء مؤشر التحميل
function hideLoading() {
  Swal.close();
}

// تصدير الدوال للاستخدام العام
window.confirmDelete = confirmDelete;
window.showSuccessMessage = showSuccessMessage;
window.showErrorMessage = showErrorMessage;
window.showLoading = showLoading;
window.hideLoading = hideLoading;