/* أنماط النماذج المحسنة */

/* حقول الإدخال الأساسية */
.form-control, .form-select {
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  color: white;
  padding: var(--space-md) var(--space-lg);
  transition: all var(--transition-normal);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-md);
}

.form-control:focus, .form-select:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--neon-blue);
  box-shadow: 0 0 0 0.25rem rgba(0, 212, 255, 0.25);
  color: white;
  outline: none;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-label {
  color: white;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-sm);
  font-size: var(--font-size-md);
}

/* مجموعة النموذج */
.form-group {
  position: relative;
  margin-bottom: var(--space-lg);
}

/* حقول الإدخال مع أيقونات */
.form-group .form-control-icon {
  padding-right: var(--space-xl);
}

.form-group .input-icon {
  position: absolute;
  right: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
  transition: all var(--transition-normal);
}

.form-group .form-control:focus + .input-icon {
  color: var(--neon-blue);
  text-shadow: 0 0 5px var(--neon-blue);
}

/* حقول الإدخال النيون */
.form-control-neon {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid var(--neon-blue);
  box-shadow: var(--neon-blue-glow);
}

.form-control-neon:focus {
  box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 212, 255, 0.5);
}

.form-control-neon-purple {
  border-color: var(--neon-purple);
  box-shadow: var(--neon-purple-glow);
}

.form-control-neon-purple:focus {
  box-shadow: 0 0 10px var(--neon-purple), 0 0 20px rgba(157, 0, 255, 0.5);
}

/* حقول الإدخال النيومورفيزم */
.form-control-neumorphism-light {
  background: var(--neumorphism-bg-light);
  box-shadow: var(--neumorphism-inset-light);
  border: none;
  color: #666;
}

.form-control-neumorphism-light:focus {
  box-shadow: var(--neumorphism-shadow-light);
}

.form-control-neumorphism-dark {
  background: var(--neumorphism-bg-dark);
  box-shadow: var(--neumorphism-inset-dark);
  border: none;
  color: #eee;
}

.form-control-neumorphism-dark:focus {
  box-shadow: var(--neumorphism-shadow-dark);
}

/* حقول الإدخال المتحركة */
.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  padding: 1.625rem var(--space-lg) 0.625rem;
}

.form-floating > label {
  padding: 1rem var(--space-lg);
  color: rgba(255, 255, 255, 0.6);
}

/* خانات الاختيار وأزرار الراديو */
.form-check {
  min-height: 1.5rem;
  margin-bottom: var(--space-md);
}

.form-check-input {
  width: 1.25em;
  height: 1.25em;
  margin-top: 0.125em;
  margin-left: -1.75em;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all var(--transition-normal);
}

.form-check-input:checked {
  background-color: var(--primary-500);
  border-color: var(--primary-500);
}

.form-check-input:focus {
  border-color: var(--neon-blue);
  box-shadow: 0 0 0 0.25rem rgba(0, 212, 255, 0.25);
}

.form-check-label {
  color: white;
  margin-right: var(--space-sm);
}

/* مفاتيح التبديل */
.form-switch .form-check-input {
  width: 2.5em;
  height: 1.25em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba(255, 255, 255, 0.5)'/%3e%3c/svg%3e");
}

.form-switch .form-check-input:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

/* مفاتيح تبديل نيون */
.form-switch-neon .form-check-input {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid var(--neon-blue);
  box-shadow: var(--neon-blue-glow);
}

.form-switch-neon .form-check-input:checked {
  background-color: var(--neon-blue);
  border-color: var(--neon-blue);
  box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 212, 255, 0.5);
}

/* مجموعة الأزرار */
.input-group {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.input-group-text {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: white;
  padding: var(--space-md) var(--space-lg);
}

/* تنسيق الرسائل */
.form-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: var(--font-size-sm);
  margin-top: var(--space-xs);
}

/* رسائل التحقق */
.valid-feedback {
  color: var(--success-500);
}

.invalid-feedback {
  color: var(--danger-500);
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: var(--success-500);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%234facfe' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: var(--danger-500);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12'%3e%3cpath fill='%23ff6a00' d='M6 0a6 6 0 1 0 0 12A6 6 0 0 0 6 0zm0 9a1 1 0 1 1 0-2 1 1 0 0 1 0 2zm0-3a1 1 0 0 1-1-1V3a1 1 0 1 1 2 0v2a1 1 0 0 1-1 1z'/%3e%3c/svg%3e");
}